#!/usr/bin/env python3
"""
Final verification that the database-driven system is ready for production
"""
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def verify_system_ready():
    """Verify that the system is ready for production use"""
    print("🔍 Final System Verification")
    print("="*50)
    
    checks_passed = 0
    total_checks = 6
    
    # Check 1: Database tables exist and are populated
    try:
        from utils.database import get_all_test_cases_metadata, get_all_test_suites_metadata
        
        test_cases = get_all_test_cases_metadata()
        test_suites = get_all_test_suites_metadata()
        
        if len(test_cases) > 0 and len(test_suites) > 0:
            print(f"✅ Database tables populated: {len(test_cases)} test cases, {len(test_suites)} test suites")
            checks_passed += 1
        else:
            print(f"❌ Database tables not properly populated")
    except Exception as e:
        print(f"❌ Database tables check failed: {str(e)}")
    
    # Check 2: UUID-based lookups work
    try:
        from utils.database import get_final_test_case_status
        
        suite_id = "90853884-1b79-4f05-8542-f590d5d307a1"
        test_case_id = "tc_bb36223ba401"
        
        result = get_final_test_case_status(suite_id=suite_id, test_case_id=test_case_id)
        
        if result and result.get('status') == 'passed':
            print(f"✅ UUID-based lookups working: {result.get('status')}")
            checks_passed += 1
        else:
            print(f"❌ UUID-based lookups failed: {result}")
    except Exception as e:
        print(f"❌ UUID-based lookups check failed: {str(e)}")
    
    # Check 3: Metadata synchronization functions exist
    try:
        from utils.metadata_sync import sync_test_case_to_database, sync_test_suite_to_database
        
        print("✅ Metadata synchronization functions available")
        checks_passed += 1
    except Exception as e:
        print(f"❌ Metadata synchronization check failed: {str(e)}")
    
    # Check 4: Enhanced report generation works
    try:
        from utils.custom_report_generator import CustomReportGenerator
        
        report_id = "testsuite_execution_20250627_181306"
        app_root_path = os.path.join(os.path.dirname(__file__), 'app')
        
        generator = CustomReportGenerator(report_id, app_root_path)
        test_data = generator._load_test_data()
        
        if test_data and len(test_data.get('testCases', test_data.get('test_cases', []))) > 0:
            print("✅ Enhanced report generation working")
            checks_passed += 1
        else:
            print("❌ Enhanced report generation failed")
    except Exception as e:
        print(f"❌ Enhanced report generation check failed: {str(e)}")
    
    # Check 5: Android version has same functions
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))
        from utils.database import get_test_case_metadata as android_get_test_case_metadata
        from utils.metadata_sync import sync_test_case_to_database as android_sync
        
        print("✅ Android version functions available")
        checks_passed += 1
    except Exception as e:
        print(f"❌ Android version check failed: {str(e)}")
    
    # Check 6: Target test case resolves correctly
    try:
        from utils.database import get_test_case_metadata_by_name
        
        metadata = get_test_case_metadata_by_name("Delivery & CNC")
        
        if metadata and metadata.get('id') == 'tc_bb36223ba401':
            print(f"✅ Target test case resolves correctly: {metadata.get('id')}")
            checks_passed += 1
        else:
            print(f"❌ Target test case resolution failed: {metadata}")
    except Exception as e:
        print(f"❌ Target test case check failed: {str(e)}")
    
    print(f"\nVerification Results: {checks_passed}/{total_checks} checks passed")
    
    if checks_passed == total_checks:
        print("\n🎉 SYSTEM READY FOR PRODUCTION!")
        print("\n📋 What to do next:")
        print("1. Restart the Flask server: python run.py (iOS) or python run_android.py (Android)")
        print("2. Test the export functionality through the web interface")
        print("3. Verify that HTML reports show correct status for 'Delivery & CNC' test case")
        print("4. The system will now automatically sync metadata when files are created/modified")
        
        return True
    else:
        print(f"\n⚠️  {total_checks - checks_passed} check(s) failed. System needs attention.")
        return False

if __name__ == "__main__":
    print("🚀 Database-Driven Mobile App Automation Tool")
    print("Final Production Readiness Verification")
    print("="*60)
    
    ready = verify_system_ready()
    
    if ready:
        print("\n" + "="*60)
        print("✅ ALL SYSTEMS GO! The database-driven system is ready!")
        print("✅ The 'Delivery & CNC' test case issue has been resolved!")
        print("✅ Reports will now show correct status information!")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("⚠️  System verification incomplete. Please check the issues above.")
        print("="*60)
