#!/usr/bin/env python3
"""
Comprehensive test suite for the database-driven system
"""
import sys
import os
import json

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_metadata_tables():
    """Test the metadata tables functionality"""
    print("🗄️  Testing Metadata Tables")
    print("="*50)
    
    try:
        from utils.database import (
            get_test_case_metadata, get_test_case_metadata_by_name, get_all_test_cases_metadata,
            get_test_suite_metadata, get_test_suite_metadata_by_name, get_all_test_suites_metadata
        )
        
        # Test getting all test cases
        all_test_cases = get_all_test_cases_metadata()
        print(f"✅ Found {len(all_test_cases)} test cases in metadata table")
        
        # Test getting all test suites
        all_test_suites = get_all_test_suites_metadata()
        print(f"✅ Found {len(all_test_suites)} test suites in metadata table")
        
        # Test specific lookups
        if all_test_cases:
            first_test_case = all_test_cases[0]
            test_case_id = first_test_case['id']
            test_case_name = first_test_case['name']
            
            # Test lookup by ID
            metadata_by_id = get_test_case_metadata(test_case_id)
            if metadata_by_id:
                print(f"✅ Successfully retrieved test case by ID: {test_case_id}")
            else:
                print(f"❌ Failed to retrieve test case by ID: {test_case_id}")
            
            # Test lookup by name
            metadata_by_name = get_test_case_metadata_by_name(test_case_name)
            if metadata_by_name:
                print(f"✅ Successfully retrieved test case by name: {test_case_name}")
            else:
                print(f"❌ Failed to retrieve test case by name: {test_case_name}")
        
        if all_test_suites:
            first_test_suite = all_test_suites[0]
            suite_id = first_test_suite['id']
            suite_name = first_test_suite['name']
            
            # Test lookup by ID
            metadata_by_id = get_test_suite_metadata(suite_id)
            if metadata_by_id:
                print(f"✅ Successfully retrieved test suite by ID: {suite_id}")
            else:
                print(f"❌ Failed to retrieve test suite by ID: {suite_id}")
            
            # Test lookup by name
            metadata_by_name = get_test_suite_metadata_by_name(suite_name)
            if metadata_by_name:
                print(f"✅ Successfully retrieved test suite by name: {suite_name}")
            else:
                print(f"❌ Failed to retrieve test suite by name: {suite_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing metadata tables: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_metadata_sync():
    """Test the metadata synchronization functionality"""
    print("\n🔄 Testing Metadata Synchronization")
    print("="*50)
    
    try:
        from utils.metadata_sync import sync_all_test_cases, sync_all_test_suites
        
        # Test syncing all test cases
        test_case_results = sync_all_test_cases()
        print(f"✅ Test cases sync: {test_case_results['success']}/{test_case_results['total']} successful")
        
        # Test syncing all test suites
        test_suite_results = sync_all_test_suites()
        print(f"✅ Test suites sync: {test_suite_results['success']}/{test_suite_results['total']} successful")
        
        return test_case_results['failed'] == 0 and test_suite_results['failed'] == 0
        
    except Exception as e:
        print(f"❌ Error testing metadata sync: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_report_generation():
    """Test the enhanced report generation with database lookups"""
    print("\n📊 Testing Enhanced Report Generation")
    print("="*50)
    
    try:
        from utils.custom_report_generator import CustomReportGenerator
        
        # Test with the known execution
        report_id = "testsuite_execution_20250627_181306"
        app_root_path = os.path.join(os.path.dirname(__file__), 'app')
        
        generator = CustomReportGenerator(report_id, app_root_path)
        print(f"✅ Created CustomReportGenerator for: {report_id}")
        print(f"   Execution ID: {generator.execution_id}")
        
        # Test data loading with database reconstruction
        test_data = generator._load_test_data()
        
        if test_data:
            test_cases = test_data.get('testCases', test_data.get('test_cases', []))
            print(f"✅ Loaded test data with {len(test_cases)} test cases")
            
            # Look for the Delivery & CNC test case
            delivery_test_case = None
            for test_case in test_cases:
                test_case_name = test_case.get('name', '')
                if 'Delivery' in test_case_name and 'CNC' in test_case_name:
                    delivery_test_case = test_case
                    break
            
            if delivery_test_case:
                print(f"✅ Found Delivery & CNC test case: {delivery_test_case.get('name')}")
                print(f"   Test Case ID: {delivery_test_case.get('test_case_id')}")
                
                # Test enhanced status determination
                final_status = generator._determine_final_test_case_status(delivery_test_case)
                print(f"   Final Status: {final_status}")
                
                if final_status == 'Passed':
                    print("✅ Enhanced report generation correctly determined status as 'Passed'")
                    return True
                else:
                    print(f"❌ Enhanced report generation returned incorrect status: {final_status}")
                    return False
            else:
                print("❌ Could not find Delivery & CNC test case in enhanced report data")
                return False
        else:
            print("❌ Failed to load test data with enhanced report generation")
            return False
        
    except Exception as e:
        print(f"❌ Error testing enhanced report generation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_uuid_based_lookups():
    """Test UUID-based database lookups"""
    print("\n🔍 Testing UUID-based Database Lookups")
    print("="*50)
    
    try:
        from utils.database import get_final_test_case_status
        
        # Test with known UUIDs from our previous analysis
        suite_id = "90853884-1b79-4f05-8542-f590d5d307a1"
        test_case_id = "tc_bb36223ba401"
        
        print(f"Testing UUID-based lookup:")
        print(f"  Suite ID: {suite_id}")
        print(f"  Test Case ID: {test_case_id}")
        
        # Test UUID-based lookup
        result = get_final_test_case_status(
            suite_id=suite_id,
            test_case_id=test_case_id
        )
        
        if result and result.get('status') == 'passed':
            print(f"✅ UUID-based lookup successful: {result.get('status')}")
            print(f"   Total actions: {result.get('total_actions')}")
            return True
        else:
            print(f"❌ UUID-based lookup failed or returned wrong status: {result}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing UUID-based lookups: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_integration():
    """Test the complete database integration"""
    print("\n🔗 Testing Complete Database Integration")
    print("="*50)
    
    try:
        from utils.database import get_test_case_metadata_by_name, get_final_test_case_status
        
        # Test the integration: metadata lookup -> status lookup
        test_case_name = "Delivery & CNC"  # Correct name from metadata table
        
        print(f"Testing integration for test case: {test_case_name}")
        
        # Step 1: Get metadata
        metadata = get_test_case_metadata_by_name(test_case_name)
        if metadata:
            print(f"✅ Found metadata for: {test_case_name}")
            print(f"   Test Case ID: {metadata.get('id')}")
            print(f"   File Path: {metadata.get('file_path')}")
            print(f"   Action Count: {metadata.get('action_count')}")
            
            # Step 2: Use metadata ID for status lookup
            test_case_id = metadata.get('id')
            suite_id = "90853884-1b79-4f05-8542-f590d5d307a1"
            
            status_result = get_final_test_case_status(
                suite_id=suite_id,
                test_case_id=test_case_id
            )
            
            if status_result and status_result.get('status') == 'passed':
                print(f"✅ Complete integration successful: {status_result.get('status')}")
                return True
            else:
                print(f"❌ Status lookup failed: {status_result}")
                return False
        else:
            print(f"❌ No metadata found for: {test_case_name}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing database integration: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Comprehensive Database-Driven System Test")
    print("="*80)
    
    # Run all tests
    tests = [
        ("Metadata Tables", test_metadata_tables),
        ("Metadata Synchronization", test_metadata_sync),
        ("UUID-based Lookups", test_uuid_based_lookups),
        ("Database Integration", test_database_integration),
        ("Enhanced Report Generation", test_enhanced_report_generation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {str(e)}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("TEST RESULTS SUMMARY")
    print("="*80)
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED! The database-driven system is working correctly!")
        print("\n📋 System Features Verified:")
        print("✅ Metadata tables for test cases and test suites")
        print("✅ UUID-based database lookups")
        print("✅ Enhanced report generation with metadata integration")
        print("✅ Automatic metadata synchronization")
        print("✅ Complete database integration workflow")
        print("\n🚀 The 'Delivery & CNC' test case issue should now be resolved!")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. System needs further investigation.")
    
    print("\n" + "="*80)
