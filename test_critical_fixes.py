#!/usr/bin/env python3
"""
Test script to validate the critical fixes for UI status update and export report issues
"""

import sys
import os
sys.path.append('.')

def test_ui_status_after_retry():
    """Test that UI status correctly shows 'passed' after retry operations"""
    print("=== Testing UI Status Update After Retry ===")
    
    from app.utils.database import get_final_test_case_status
    
    # Test with the known test case that has retry updates
    suite_id = "testsuite_execution_20250627_181306"
    filename = "Delivery & CNC Stop"
    
    print(f"Testing status retrieval for suite: {suite_id}")
    print(f"Test case: {filename}")
    
    status_data = get_final_test_case_status(
        suite_id=suite_id,
        filename=filename
    )
    
    final_status = status_data.get('status', 'unknown')
    actions = status_data.get('actions', {})
    
    print(f"Final test case status: {final_status}")
    print(f"Number of actions found: {len(actions)}")
    
    # Check if all actions have 'passed' status (since they were retried successfully)
    passed_actions = 0
    failed_actions = 0
    
    for action_id, action_info in actions.items():
        action_status = action_info.get('status', 'unknown')
        if action_status == 'passed':
            passed_actions += 1
        elif action_status == 'failed':
            failed_actions += 1
    
    print(f"Actions with 'passed' status: {passed_actions}")
    print(f"Actions with 'failed' status: {failed_actions}")
    
    # The fix should ensure that:
    # 1. Final status is 'passed' (since all actions were retried and passed)
    # 2. All actions show 'passed' status (from retry_update entries)
    success = (final_status == 'passed' and failed_actions == 0 and passed_actions > 0)
    
    print(f"UI Status Update Fix: {'PASSED' if success else 'FAILED'}")
    if not success:
        print(f"  Expected: final_status='passed', failed_actions=0, passed_actions>0")
        print(f"  Actual: final_status='{final_status}', failed_actions={failed_actions}, passed_actions={passed_actions}")
    
    return success

def test_export_report_completeness():
    """Test that export reports include all test cases, not just the retried one"""
    print("\n=== Testing Export Report Completeness ===")
    
    from app.utils.custom_report_generator import CustomReportGenerator
    
    # Test with the known execution ID that had multiple test cases
    execution_id = "testsuite_execution_20250627_181306"
    app_root_path = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app"
    
    print(f"Testing export generation for execution: {execution_id}")
    
    try:
        generator = CustomReportGenerator(execution_id, app_root_path)
        
        # Test data loading (this should include all test cases)
        test_data = generator._load_test_data()
        
        if not test_data:
            print("FAILED: Could not load test data")
            return False
        
        test_cases = test_data.get('testCases', [])
        num_test_cases = len(test_cases)
        
        print(f"Test suite name: {test_data.get('name', 'Unknown')}")
        print(f"Number of test cases found: {num_test_cases}")
        
        # Find the retried test case and check its status
        retried_test_case = None
        for tc in test_cases:
            if 'Delivery & CNC' in tc.get('name', ''):
                retried_test_case = tc
                break
        
        if retried_test_case:
            print(f"Found retried test case: {retried_test_case.get('name')}")
            print(f"Retried test case status: {retried_test_case.get('status')}")
            
            # Check if steps have proper status (not 'Unknown')
            steps = retried_test_case.get('steps', [])
            unknown_steps = [s for s in steps if s.get('status') == 'Unknown']
            print(f"Steps with 'Unknown' status: {len(unknown_steps)} out of {len(steps)}")
        else:
            print("WARNING: Could not find the retried test case")
        
        # The fix should ensure that:
        # 1. Multiple test cases are included (not just 1)
        # 2. Action steps don't have 'Unknown' status
        success = (num_test_cases > 1)
        
        print(f"Export Report Completeness Fix: {'PASSED' if success else 'FAILED'}")
        if not success:
            print(f"  Expected: num_test_cases > 1")
            print(f"  Actual: num_test_cases = {num_test_cases}")
        
        return success
        
    except Exception as e:
        print(f"FAILED: Error testing export generation: {e}")
        return False

def test_status_consistency():
    """Test that the status from database matches what should be shown in UI"""
    print("\n=== Testing Status Consistency ===")
    
    from app.utils.database import get_final_test_case_status
    
    # Test the specific case mentioned in the issue
    suite_id = "testsuite_execution_20250627_181306"
    filename = "Delivery & CNC Stop"
    
    # Get status using the fixed function
    status_data = get_final_test_case_status(suite_id=suite_id, filename=filename)
    final_status = status_data.get('status', 'unknown')
    
    print(f"Database reports final status as: {final_status}")
    
    # Since we know this test case was retried and all actions passed,
    # the status should be 'passed', not 'failed'
    success = (final_status == 'passed')
    
    print(f"Status Consistency Fix: {'PASSED' if success else 'FAILED'}")
    if not success:
        print(f"  Expected: 'passed' (since retry operations succeeded)")
        print(f"  Actual: '{final_status}'")
    
    return success

def main():
    """Run critical tests to validate the fixes"""
    print("Testing critical fixes for UI status update and export report issues\n")
    
    # Test 1: UI Status Update (most critical)
    ui_test_passed = test_ui_status_after_retry()
    
    # Test 2: Export Report Completeness (second most critical)
    export_test_passed = test_export_report_completeness()
    
    # Test 3: Status Consistency
    consistency_test_passed = test_status_consistency()
    
    # Summary
    print("\n" + "="*50)
    print("CRITICAL TEST RESULTS SUMMARY")
    print("="*50)
    print(f"✓ UI Status Update Fix: {'PASSED' if ui_test_passed else 'FAILED'}")
    print(f"✓ Export Report Completeness Fix: {'PASSED' if export_test_passed else 'FAILED'}")
    print(f"✓ Status Consistency Fix: {'PASSED' if consistency_test_passed else 'FAILED'}")
    
    all_passed = ui_test_passed and export_test_passed and consistency_test_passed
    print(f"\n🎯 Overall Result: {'ALL CRITICAL FIXES WORKING' if all_passed else 'SOME FIXES NEED ATTENTION'}")
    
    if all_passed:
        print("\n✅ The fixes successfully address both reported issues:")
        print("   1. UI now shows correct 'passed' status after retry operations")
        print("   2. Export reports include all test cases with correct statuses")
    else:
        print("\n❌ Some issues remain - please check the failed tests above")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
