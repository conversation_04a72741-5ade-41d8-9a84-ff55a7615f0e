#!/usr/bin/env python3
"""
Test script to validate the action details fix in export reports
"""

import sys
import os
sys.path.append('.')

def test_action_details_in_export():
    """Test that export reports show proper action types instead of 'Unknown action'"""
    print("=== Testing Action Details Fix in Export Reports ===")
    
    from app.utils.custom_report_generator import CustomReportGenerator
    
    # Test with the known execution ID
    execution_id = "testsuite_execution_20250627_181306"
    app_root_path = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app"
    
    print(f"Testing action details for execution: {execution_id}")
    
    try:
        generator = CustomReportGenerator(execution_id, app_root_path)
        
        # Test database reconstruction with hybrid approach
        test_data = generator._reconstruct_test_data_from_database()
        
        if not test_data:
            print("FAILED: Could not reconstruct test data")
            return False
        
        test_cases = test_data.get('testCases', [])
        print(f"Found {len(test_cases)} test cases")
        
        # Find the retried test case
        retried_test_case = None
        for tc in test_cases:
            if 'Delivery & CNC' in tc.get('name', ''):
                retried_test_case = tc
                break
        
        if not retried_test_case:
            print("FAILED: Could not find the retried test case")
            return False
        
        print(f"Testing action details for: {retried_test_case.get('name')}")
        
        steps = retried_test_case.get('steps', [])
        print(f"Number of steps: {len(steps)}")
        
        # Check action types
        unknown_actions = 0
        proper_actions = 0
        action_types_found = set()
        
        for i, step in enumerate(steps[:10]):  # Check first 10 steps
            action_type = step.get('action_type', 'Unknown')
            action_id = step.get('action_id', 'N/A')
            status = step.get('status', 'Unknown')
            
            if action_type == 'Unknown' or action_type == 'retry_update':
                unknown_actions += 1
                print(f"  Step {i+1}: Action ID {action_id} - Type: {action_type} (Status: {status}) ❌")
            else:
                proper_actions += 1
                action_types_found.add(action_type)
                print(f"  Step {i+1}: Action ID {action_id} - Type: {action_type} (Status: {status}) ✅")
        
        print(f"\nAction Types Summary:")
        print(f"  Proper action types: {proper_actions}")
        print(f"  Unknown/retry_update actions: {unknown_actions}")
        print(f"  Unique action types found: {sorted(action_types_found)}")
        
        # Success criteria: Most actions should have proper types, not 'Unknown' or 'retry_update'
        success = proper_actions > unknown_actions and len(action_types_found) > 0
        
        print(f"\nAction Details Fix: {'PASSED' if success else 'FAILED'}")
        if not success:
            print(f"  Expected: proper_actions > unknown_actions and unique_types > 0")
            print(f"  Actual: proper_actions={proper_actions}, unknown_actions={unknown_actions}, unique_types={len(action_types_found)}")
        
        return success
        
    except Exception as e:
        print(f"FAILED: Error testing action details: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hybrid_data_approach():
    """Test that the hybrid approach correctly combines database status with file metadata"""
    print("\n=== Testing Hybrid Data Approach ===")
    
    from app.utils.custom_report_generator import CustomReportGenerator
    
    execution_id = "testsuite_execution_20250627_181306"
    app_root_path = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app"
    
    try:
        generator = CustomReportGenerator(execution_id, app_root_path)
        
        # Test the complete data loading process
        test_data = generator._load_test_data()
        
        if not test_data:
            print("FAILED: Could not load test data")
            return False
        
        test_cases = test_data.get('testCases', [])
        print(f"Loaded {len(test_cases)} test cases using hybrid approach")
        
        # Check if we have both proper action types and latest status
        retried_test_case = None
        for tc in test_cases:
            if 'Delivery & CNC' in tc.get('name', ''):
                retried_test_case = tc
                break
        
        if retried_test_case:
            print(f"Retried test case: {retried_test_case.get('name')}")
            print(f"Final status: {retried_test_case.get('status')}")
            
            steps = retried_test_case.get('steps', [])
            
            # Check for hybrid data quality
            has_proper_types = False
            has_latest_status = False
            
            for step in steps[:5]:  # Check first 5 steps
                action_type = step.get('action_type', 'Unknown')
                status = step.get('status', 'Unknown')
                
                if action_type not in ['Unknown', 'retry_update']:
                    has_proper_types = True
                
                if status == 'passed':  # We know this test case was retried and passed
                    has_latest_status = True
            
            print(f"Has proper action types: {has_proper_types}")
            print(f"Has latest status from database: {has_latest_status}")
            
            success = has_proper_types and has_latest_status
            print(f"Hybrid Data Approach: {'PASSED' if success else 'FAILED'}")
            return success
        else:
            print("FAILED: Could not find retried test case")
            return False
            
    except Exception as e:
        print(f"FAILED: Error testing hybrid approach: {e}")
        return False

def main():
    """Run all tests for action details and hybrid approach"""
    print("Testing action details fix and hybrid data approach\n")
    
    # Test 1: Action Details Fix
    action_details_passed = test_action_details_in_export()
    
    # Test 2: Hybrid Data Approach
    hybrid_approach_passed = test_hybrid_data_approach()
    
    # Summary
    print("\n" + "="*60)
    print("ACTION DETAILS AND HYBRID APPROACH TEST RESULTS")
    print("="*60)
    print(f"✓ Action Details Fix: {'PASSED' if action_details_passed else 'FAILED'}")
    print(f"✓ Hybrid Data Approach: {'PASSED' if hybrid_approach_passed else 'FAILED'}")
    
    all_passed = action_details_passed and hybrid_approach_passed
    print(f"\n🎯 Overall Result: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n✅ Export reports should now show:")
        print("   - Proper action types (tap, swipe, input, etc.) instead of 'Unknown action'")
        print("   - Latest execution status from database (reflecting retry results)")
        print("   - Complete test suite with all test cases")
        print("   - Enhanced action details from original test case files")
    else:
        print("\n❌ Some issues remain with action details or hybrid approach")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
