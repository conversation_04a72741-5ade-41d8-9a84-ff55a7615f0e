#!/usr/bin/env python3
"""
Test script to verify the API endpoint functionality
"""

import requests
import json
import sys

def test_update_retry_results_endpoint():
    """Test the /api/execution/update-retry-results endpoint"""
    
    # Test data
    test_data = {
        "execution_id": "test_execution_123",
        "retry_results": {
            "testCaseName": "Test Case",
            "test_case_index": 0,
            "status": "passed",
            "test_case_id": "test_case_123",
            "actions": [
                {
                    "action_id": "action_123",
                    "status": "passed",
                    "retry_count": 1,
                    "error": None
                }
            ]
        }
    }
    
    # Test URLs for both iOS and Android
    test_urls = [
        "http://localhost:8080/api/execution/update-retry-results",  # iOS
        "http://localhost:8081/api/execution/update-retry-results"   # Android
    ]
    
    for url in test_urls:
        print(f"\nTesting endpoint: {url}")
        
        try:
            response = requests.post(
                url,
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                print("✅ Endpoint is working correctly")
            else:
                print("❌ Endpoint returned an error")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - server not running")
        except requests.exceptions.Timeout:
            print("❌ Request timed out")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def test_endpoint_validation():
    """Test endpoint validation with invalid data"""
    
    # Test with missing data
    invalid_data = {
        "execution_id": "test_execution_123"
        # Missing retry_results
    }
    
    url = "http://localhost:8080/api/execution/update-retry-results"
    
    print(f"\nTesting validation with invalid data: {url}")
    
    try:
        response = requests.post(
            url,
            json=invalid_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 400:
            print("✅ Validation is working correctly")
        else:
            print("❌ Validation failed")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - server not running")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("Testing API Endpoint Functionality")
    print("=" * 50)
    
    # Test basic functionality
    test_update_retry_results_endpoint()
    
    # Test validation
    test_endpoint_validation()
    
    print("\n" + "=" * 50)
    print("Test completed!")
