Action Log - 2025-06-27 19:20:45
================================================================================

[[19:20:44]] [INFO] Generating execution report...
[[19:20:44]] [WARNING] 1 test failed.
[[19:20:44]] [SUCCESS] Screenshot refreshed
[[19:20:44]] [INFO] Refreshing screenshot...
[[19:20:44]] [INFO] Ll4UlkE3L9=pass
[[19:20:19]] [SUCCESS] Screenshot refreshed successfully
[[19:20:19]] [SUCCESS] Screenshot refreshed successfully
[[19:20:18]] [INFO] Ll4UlkE3L9=running
[[19:20:18]] [INFO] Executing action 576/576: cleanupSteps action
[[19:20:17]] [SUCCESS] Screenshot refreshed
[[19:20:17]] [INFO] Refreshing screenshot...
[[19:20:17]] [INFO] 25UEKPIknm=pass
[[19:20:14]] [SUCCESS] Screenshot refreshed successfully
[[19:20:14]] [SUCCESS] Screenshot refreshed successfully
[[19:20:14]] [INFO] 25UEKPIknm=running
[[19:20:14]] [INFO] Executing action 575/576: Terminate app: env[appid]
[[19:20:14]] [SUCCESS] Screenshot refreshed
[[19:20:14]] [INFO] Refreshing screenshot...
[[19:20:14]] [INFO] UqgDn5CuPY=pass
[[19:20:10]] [SUCCESS] Screenshot refreshed successfully
[[19:20:10]] [SUCCESS] Screenshot refreshed successfully
[[19:20:10]] [INFO] UqgDn5CuPY=running
[[19:20:10]] [INFO] Executing action 574/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[19:20:10]] [SUCCESS] Screenshot refreshed
[[19:20:10]] [INFO] Refreshing screenshot...
[[19:20:10]] [INFO] VfTTTtrliQ=pass
[[19:20:07]] [SUCCESS] Screenshot refreshed successfully
[[19:20:07]] [SUCCESS] Screenshot refreshed successfully
[[19:20:07]] [INFO] VfTTTtrliQ=running
[[19:20:07]] [INFO] Executing action 573/576: iOS Function: alert_accept
[[19:20:06]] [SUCCESS] Screenshot refreshed
[[19:20:06]] [INFO] Refreshing screenshot...
[[19:20:06]] [INFO] ipT2XD9io6=pass
[[19:20:02]] [SUCCESS] Screenshot refreshed successfully
[[19:20:02]] [SUCCESS] Screenshot refreshed successfully
[[19:20:02]] [INFO] ipT2XD9io6=running
[[19:20:02]] [INFO] Executing action 572/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[19:20:02]] [SUCCESS] Screenshot refreshed
[[19:20:02]] [INFO] Refreshing screenshot...
[[19:20:02]] [INFO] OKCHAK6HCJ=pass
[[19:19:58]] [SUCCESS] Screenshot refreshed successfully
[[19:19:58]] [SUCCESS] Screenshot refreshed successfully
[[19:19:58]] [INFO] OKCHAK6HCJ=running
[[19:19:58]] [INFO] Executing action 571/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:19:57]] [SUCCESS] Screenshot refreshed
[[19:19:57]] [INFO] Refreshing screenshot...
[[19:19:57]] [INFO] AEnFqnkOa1=pass
[[19:19:53]] [SUCCESS] Screenshot refreshed successfully
[[19:19:53]] [SUCCESS] Screenshot refreshed successfully
[[19:19:53]] [INFO] AEnFqnkOa1=running
[[19:19:53]] [INFO] Executing action 570/576: Tap on image: banner-close-updated.png
[[19:19:52]] [SUCCESS] Screenshot refreshed
[[19:19:52]] [INFO] Refreshing screenshot...
[[19:19:52]] [INFO] z1CfcW4xYT=pass
[[19:19:48]] [SUCCESS] Screenshot refreshed successfully
[[19:19:48]] [SUCCESS] Screenshot refreshed successfully
[[19:19:48]] [INFO] z1CfcW4xYT=running
[[19:19:48]] [INFO] Executing action 569/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[19:19:48]] [SUCCESS] Screenshot refreshed
[[19:19:48]] [INFO] Refreshing screenshot...
[[19:19:48]] [INFO] dJNRgTXoqs=pass
[[19:19:43]] [SUCCESS] Screenshot refreshed successfully
[[19:19:43]] [SUCCESS] Screenshot refreshed successfully
[[19:19:43]] [INFO] dJNRgTXoqs=running
[[19:19:43]] [INFO] Executing action 568/576: Swipe from (50%, 30%) to (50%, 70%)
[[19:19:43]] [SUCCESS] Screenshot refreshed
[[19:19:43]] [INFO] Refreshing screenshot...
[[19:19:43]] [INFO] ceF4VRTJlO=pass
[[19:19:39]] [SUCCESS] Screenshot refreshed successfully
[[19:19:39]] [SUCCESS] Screenshot refreshed successfully
[[19:19:39]] [INFO] ceF4VRTJlO=running
[[19:19:39]] [INFO] Executing action 567/576: Tap on image: banner-close-updated.png
[[19:19:38]] [SUCCESS] Screenshot refreshed
[[19:19:38]] [INFO] Refreshing screenshot...
[[19:19:38]] [INFO] 8hCPyY2zPt=pass
[[19:19:34]] [SUCCESS] Screenshot refreshed successfully
[[19:19:34]] [SUCCESS] Screenshot refreshed successfully
[[19:19:34]] [INFO] 8hCPyY2zPt=running
[[19:19:34]] [INFO] Executing action 566/576: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[19:19:34]] [SUCCESS] Screenshot refreshed
[[19:19:34]] [INFO] Refreshing screenshot...
[[19:19:34]] [INFO] r0FfJ85LFM=pass
[[19:19:30]] [SUCCESS] Screenshot refreshed successfully
[[19:19:30]] [SUCCESS] Screenshot refreshed successfully
[[19:19:30]] [INFO] r0FfJ85LFM=running
[[19:19:30]] [INFO] Executing action 565/576: Tap on image: banner-close-updated.png
[[19:19:29]] [SUCCESS] Screenshot refreshed
[[19:19:29]] [INFO] Refreshing screenshot...
[[19:19:29]] [INFO] 2QEdm5WM18=pass
[[19:19:25]] [SUCCESS] Screenshot refreshed successfully
[[19:19:25]] [SUCCESS] Screenshot refreshed successfully
[[19:19:25]] [INFO] 2QEdm5WM18=running
[[19:19:25]] [INFO] Executing action 564/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[19:19:24]] [SUCCESS] Screenshot refreshed
[[19:19:24]] [INFO] Refreshing screenshot...
[[19:19:24]] [INFO] NW6M15JbAy=pass
[[19:19:11]] [SUCCESS] Screenshot refreshed successfully
[[19:19:11]] [SUCCESS] Screenshot refreshed successfully
[[19:19:11]] [INFO] NW6M15JbAy=running
[[19:19:11]] [INFO] Executing action 563/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[19:19:11]] [SUCCESS] Screenshot refreshed
[[19:19:11]] [INFO] Refreshing screenshot...
[[19:19:11]] [INFO] njiHWyVooT=pass
[[19:19:08]] [SUCCESS] Screenshot refreshed successfully
[[19:19:08]] [SUCCESS] Screenshot refreshed successfully
[[19:19:07]] [INFO] njiHWyVooT=running
[[19:19:07]] [INFO] Executing action 562/576: Tap on image: banner-close-updated.png
[[19:19:06]] [SUCCESS] Screenshot refreshed
[[19:19:06]] [INFO] Refreshing screenshot...
[[19:19:06]] [INFO] 93bAew9Y4Y=pass
[[19:19:02]] [SUCCESS] Screenshot refreshed successfully
[[19:19:02]] [SUCCESS] Screenshot refreshed successfully
[[19:19:02]] [INFO] 93bAew9Y4Y=running
[[19:19:02]] [INFO] Executing action 561/576: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[19:19:01]] [SUCCESS] Screenshot refreshed
[[19:19:01]] [INFO] Refreshing screenshot...
[[19:19:01]] [INFO] rPQ5EkTza1=pass
[[19:18:57]] [SUCCESS] Screenshot refreshed successfully
[[19:18:57]] [SUCCESS] Screenshot refreshed successfully
[[19:18:56]] [INFO] rPQ5EkTza1=running
[[19:18:56]] [INFO] Executing action 560/576: Tap on Text: "Click"
[[19:18:56]] [SUCCESS] Screenshot refreshed
[[19:18:56]] [INFO] Refreshing screenshot...
[[19:18:56]] [SUCCESS] Screenshot refreshed
[[19:18:56]] [INFO] Refreshing screenshot...
[[19:18:51]] [INFO] Executing Multi Step action step 6/6: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[19:18:51]] [SUCCESS] Screenshot refreshed successfully
[[19:18:51]] [SUCCESS] Screenshot refreshed successfully
[[19:18:51]] [SUCCESS] Screenshot refreshed
[[19:18:51]] [INFO] Refreshing screenshot...
[[19:18:47]] [SUCCESS] Screenshot refreshed successfully
[[19:18:47]] [SUCCESS] Screenshot refreshed successfully
[[19:18:47]] [INFO] Executing Multi Step action step 5/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[19:18:46]] [SUCCESS] Screenshot refreshed
[[19:18:46]] [INFO] Refreshing screenshot...
[[19:18:42]] [SUCCESS] Screenshot refreshed successfully
[[19:18:42]] [SUCCESS] Screenshot refreshed successfully
[[19:18:42]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[19:18:41]] [SUCCESS] Screenshot refreshed
[[19:18:41]] [INFO] Refreshing screenshot...
[[19:18:36]] [SUCCESS] Screenshot refreshed successfully
[[19:18:36]] [SUCCESS] Screenshot refreshed successfully
[[19:18:36]] [INFO] Executing Multi Step action step 3/6: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[19:18:35]] [SUCCESS] Screenshot refreshed
[[19:18:35]] [INFO] Refreshing screenshot...
[[19:18:31]] [SUCCESS] Screenshot refreshed successfully
[[19:18:31]] [SUCCESS] Screenshot refreshed successfully
[[19:18:31]] [INFO] Executing Multi Step action step 2/6: iOS Function: text - Text: "Notebook"
[[19:18:30]] [SUCCESS] Screenshot refreshed
[[19:18:30]] [INFO] Refreshing screenshot...
[[19:18:23]] [SUCCESS] Screenshot refreshed successfully
[[19:18:23]] [SUCCESS] Screenshot refreshed successfully
[[19:18:23]] [INFO] Executing Multi Step action step 1/6: Tap on Text: "Find"
[[19:18:23]] [INFO] Loaded 6 steps from test case: Search and Add (Notebooks)
[[19:18:23]] [INFO] Loading steps for Multi Step action: Search and Add (Notebooks)
[[19:18:23]] [INFO] 0YgZZfWdYY=running
[[19:18:23]] [INFO] Executing action 559/576: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[19:18:22]] [SUCCESS] Screenshot refreshed
[[19:18:22]] [INFO] Refreshing screenshot...
[[19:18:22]] [INFO] arH1CZCPXh=pass
[[19:18:17]] [SUCCESS] Screenshot refreshed successfully
[[19:18:17]] [SUCCESS] Screenshot refreshed successfully
[[19:18:17]] [INFO] arH1CZCPXh=running
[[19:18:17]] [INFO] Executing action 558/576: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[19:18:16]] [SUCCESS] Screenshot refreshed
[[19:18:16]] [INFO] Refreshing screenshot...
[[19:18:16]] [INFO] JLAJhxPdsl=pass
[[19:18:11]] [SUCCESS] Screenshot refreshed successfully
[[19:18:11]] [SUCCESS] Screenshot refreshed successfully
[[19:18:11]] [INFO] JLAJhxPdsl=running
[[19:18:11]] [INFO] Executing action 557/576: Tap on Text: "Cancel"
[[19:18:10]] [SUCCESS] Screenshot refreshed
[[19:18:10]] [INFO] Refreshing screenshot...
[[19:18:10]] [INFO] UqgDn5CuPY=pass
[[19:18:09]] [SUCCESS] Screenshot refreshed successfully
[[19:18:09]] [SUCCESS] Screenshot refreshed successfully
[[19:18:06]] [INFO] UqgDn5CuPY=running
[[19:18:06]] [INFO] Executing action 556/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[19:18:06]] [SUCCESS] Screenshot refreshed
[[19:18:06]] [INFO] Refreshing screenshot...
[[19:18:06]] [INFO] VfTTTtrliQ=pass
[[19:18:04]] [SUCCESS] Screenshot refreshed successfully
[[19:18:04]] [SUCCESS] Screenshot refreshed successfully
[[19:18:03]] [INFO] VfTTTtrliQ=running
[[19:18:03]] [INFO] Executing action 555/576: iOS Function: alert_accept
[[19:18:03]] [SUCCESS] Screenshot refreshed
[[19:18:03]] [INFO] Refreshing screenshot...
[[19:18:03]] [INFO] ipT2XD9io6=pass
[[19:17:59]] [SUCCESS] Screenshot refreshed successfully
[[19:17:59]] [SUCCESS] Screenshot refreshed successfully
[[19:17:58]] [INFO] ipT2XD9io6=running
[[19:17:58]] [INFO] Executing action 554/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[19:17:58]] [SUCCESS] Screenshot refreshed
[[19:17:58]] [INFO] Refreshing screenshot...
[[19:17:58]] [INFO] OKCHAK6HCJ=pass
[[19:17:54]] [SUCCESS] Screenshot refreshed successfully
[[19:17:54]] [SUCCESS] Screenshot refreshed successfully
[[19:17:53]] [INFO] OKCHAK6HCJ=running
[[19:17:53]] [INFO] Executing action 553/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[19:17:53]] [SUCCESS] Screenshot refreshed
[[19:17:53]] [INFO] Refreshing screenshot...
[[19:17:53]] [INFO] RbD937Xbte=pass
[[19:17:48]] [SUCCESS] Screenshot refreshed successfully
[[19:17:48]] [SUCCESS] Screenshot refreshed successfully
[[19:17:48]] [INFO] RbD937Xbte=running
[[19:17:48]] [INFO] Executing action 552/576: Tap on Text: "out"
[[19:17:47]] [SUCCESS] Screenshot refreshed
[[19:17:47]] [INFO] Refreshing screenshot...
[[19:17:47]] [INFO] ylslyLAYKb=pass
[[19:17:44]] [SUCCESS] Screenshot refreshed successfully
[[19:17:44]] [SUCCESS] Screenshot refreshed successfully
[[19:17:43]] [INFO] ylslyLAYKb=running
[[19:17:43]] [INFO] Executing action 551/576: Swipe from (50%, 70%) to (50%, 30%)
[[19:17:43]] [SUCCESS] Screenshot refreshed
[[19:17:43]] [INFO] Refreshing screenshot...
[[19:17:43]] [INFO] wguGCt7OoB=pass
[[19:17:39]] [SUCCESS] Screenshot refreshed successfully
[[19:17:39]] [SUCCESS] Screenshot refreshed successfully
[[19:17:39]] [INFO] wguGCt7OoB=running
[[19:17:39]] [INFO] Executing action 550/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:17:39]] [SUCCESS] Screenshot refreshed
[[19:17:39]] [INFO] Refreshing screenshot...
[[19:17:39]] [INFO] RDQCFIxjA0=pass
[[19:17:35]] [SUCCESS] Screenshot refreshed successfully
[[19:17:35]] [SUCCESS] Screenshot refreshed successfully
[[19:17:35]] [INFO] RDQCFIxjA0=running
[[19:17:35]] [INFO] Executing action 549/576: Swipe from (90%, 20%) to (30%, 20%)
[[19:17:34]] [SUCCESS] Screenshot refreshed
[[19:17:34]] [INFO] Refreshing screenshot...
[[19:17:34]] [INFO] x4Mid4HQ0Z=pass
[[19:17:31]] [SUCCESS] Screenshot refreshed successfully
[[19:17:31]] [SUCCESS] Screenshot refreshed successfully
[[19:17:31]] [INFO] x4Mid4HQ0Z=running
[[19:17:31]] [INFO] Executing action 548/576: Swipe from (90%, 20%) to (30%, 20%)
[[19:17:30]] [SUCCESS] Screenshot refreshed
[[19:17:30]] [INFO] Refreshing screenshot...
[[19:17:30]] [INFO] OKCHAK6HCJ=pass
[[19:17:26]] [SUCCESS] Screenshot refreshed successfully
[[19:17:26]] [SUCCESS] Screenshot refreshed successfully
[[19:17:26]] [INFO] OKCHAK6HCJ=running
[[19:17:26]] [INFO] Executing action 547/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[19:17:25]] [SUCCESS] Screenshot refreshed
[[19:17:25]] [INFO] Refreshing screenshot...
[[19:17:25]] [INFO] Ef6OumM2eS=pass
[[19:17:21]] [SUCCESS] Screenshot refreshed successfully
[[19:17:21]] [SUCCESS] Screenshot refreshed successfully
[[19:17:21]] [INFO] Ef6OumM2eS=running
[[19:17:21]] [INFO] Executing action 546/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[19:17:20]] [SUCCESS] Screenshot refreshed
[[19:17:20]] [INFO] Refreshing screenshot...
[[19:17:20]] [INFO] QkaF93zxUg=pass
[[19:17:17]] [SUCCESS] Screenshot refreshed successfully
[[19:17:17]] [SUCCESS] Screenshot refreshed successfully
[[19:17:17]] [INFO] QkaF93zxUg=running
[[19:17:17]] [INFO] Executing action 545/576: Check if element with xpath="(//XCUIElementTypeStaticText[@name="Value"])[1]" exists
[[19:17:16]] [SUCCESS] Screenshot refreshed
[[19:17:16]] [INFO] Refreshing screenshot...
[[19:17:16]] [INFO] HZT2s0AzX7=pass
[[19:17:12]] [SUCCESS] Screenshot refreshed successfully
[[19:17:12]] [SUCCESS] Screenshot refreshed successfully
[[19:17:12]] [INFO] HZT2s0AzX7=running
[[19:17:12]] [INFO] Executing action 544/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="("]/following-sibling::*[1]
[[19:17:11]] [SUCCESS] Screenshot refreshed
[[19:17:11]] [INFO] Refreshing screenshot...
[[19:17:11]] [INFO] 0bnBNoqPt8=pass
[[19:17:09]] [SUCCESS] Screenshot refreshed successfully
[[19:17:09]] [SUCCESS] Screenshot refreshed successfully
[[19:17:07]] [INFO] 0bnBNoqPt8=running
[[19:17:07]] [INFO] Executing action 543/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[19:17:06]] [SUCCESS] Screenshot refreshed
[[19:17:06]] [INFO] Refreshing screenshot...
[[19:17:06]] [INFO] xmelRkcdVx=pass
[[19:17:02]] [SUCCESS] Screenshot refreshed successfully
[[19:17:02]] [SUCCESS] Screenshot refreshed successfully
[[19:17:02]] [INFO] xmelRkcdVx=running
[[19:17:02]] [INFO] Executing action 542/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[19:17:01]] [SUCCESS] Screenshot refreshed
[[19:17:01]] [INFO] Refreshing screenshot...
[[19:17:01]] [INFO] ksCBjJiwHZ=pass
[[19:16:58]] [SUCCESS] Screenshot refreshed successfully
[[19:16:58]] [SUCCESS] Screenshot refreshed successfully
[[19:16:57]] [INFO] ksCBjJiwHZ=running
[[19:16:57]] [INFO] Executing action 541/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[19:16:57]] [SUCCESS] Screenshot refreshed
[[19:16:57]] [INFO] Refreshing screenshot...
[[19:16:57]] [INFO] RuPGkdCdah=pass
[[19:16:53]] [SUCCESS] Screenshot refreshed successfully
[[19:16:53]] [SUCCESS] Screenshot refreshed successfully
[[19:16:53]] [INFO] RuPGkdCdah=running
[[19:16:53]] [INFO] Executing action 540/576: iOS Function: text - Text: "enn[cooker-id]"
[[19:16:52]] [SUCCESS] Screenshot refreshed
[[19:16:52]] [INFO] Refreshing screenshot...
[[19:16:52]] [INFO] ewuLtuqVuo=pass
[[19:16:47]] [SUCCESS] Screenshot refreshed successfully
[[19:16:47]] [SUCCESS] Screenshot refreshed successfully
[[19:16:47]] [INFO] ewuLtuqVuo=running
[[19:16:47]] [INFO] Executing action 539/576: Tap on Text: "Find"
[[19:16:46]] [SUCCESS] Screenshot refreshed
[[19:16:46]] [INFO] Refreshing screenshot...
[[19:16:46]] [INFO] GTXmST3hEA=pass
[[19:16:42]] [SUCCESS] Screenshot refreshed successfully
[[19:16:42]] [SUCCESS] Screenshot refreshed successfully
[[19:16:41]] [INFO] GTXmST3hEA=running
[[19:16:41]] [INFO] Executing action 538/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[19:16:41]] [SUCCESS] Screenshot refreshed
[[19:16:41]] [INFO] Refreshing screenshot...
[[19:16:41]] [INFO] qkZ5KShdEU=pass
[[19:16:35]] [SUCCESS] Screenshot refreshed successfully
[[19:16:35]] [SUCCESS] Screenshot refreshed successfully
[[19:16:35]] [INFO] qkZ5KShdEU=running
[[19:16:35]] [INFO] Executing action 537/576: iOS Function: text - Text: "env[pwd]"
[[19:16:35]] [SUCCESS] Screenshot refreshed
[[19:16:35]] [INFO] Refreshing screenshot...
[[19:16:35]] [INFO] 7g2LmvjtEZ=pass
[[19:16:30]] [SUCCESS] Screenshot refreshed successfully
[[19:16:30]] [SUCCESS] Screenshot refreshed successfully
[[19:16:30]] [INFO] 7g2LmvjtEZ=running
[[19:16:30]] [INFO] Executing action 536/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[19:16:30]] [SUCCESS] Screenshot refreshed
[[19:16:30]] [INFO] Refreshing screenshot...
[[19:16:30]] [INFO] OUT2ASweb6=pass
[[19:16:25]] [SUCCESS] Screenshot refreshed successfully
[[19:16:25]] [SUCCESS] Screenshot refreshed successfully
[[19:16:25]] [INFO] OUT2ASweb6=running
[[19:16:25]] [INFO] Executing action 535/576: iOS Function: text - Text: "env[uname]"
[[19:16:25]] [SUCCESS] Screenshot refreshed
[[19:16:25]] [INFO] Refreshing screenshot...
[[19:16:25]] [INFO] TV4kJIIV9v=pass
[[19:16:20]] [SUCCESS] Screenshot refreshed successfully
[[19:16:20]] [SUCCESS] Screenshot refreshed successfully
[[19:16:20]] [INFO] TV4kJIIV9v=running
[[19:16:20]] [INFO] Executing action 534/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[19:16:20]] [SUCCESS] Screenshot refreshed
[[19:16:20]] [INFO] Refreshing screenshot...
[[19:16:20]] [INFO] kQJbqm7uCi=pass
[[19:16:17]] [SUCCESS] Screenshot refreshed successfully
[[19:16:17]] [SUCCESS] Screenshot refreshed successfully
[[19:16:17]] [INFO] kQJbqm7uCi=running
[[19:16:17]] [INFO] Executing action 533/576: iOS Function: alert_accept
[[19:16:16]] [SUCCESS] Screenshot refreshed
[[19:16:16]] [INFO] Refreshing screenshot...
[[19:16:16]] [INFO] SPE01N6pyp=pass
[[19:16:11]] [SUCCESS] Screenshot refreshed successfully
[[19:16:11]] [SUCCESS] Screenshot refreshed successfully
[[19:16:10]] [INFO] SPE01N6pyp=running
[[19:16:10]] [INFO] Executing action 532/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[19:16:10]] [SUCCESS] Screenshot refreshed
[[19:16:10]] [INFO] Refreshing screenshot...
[[19:16:10]] [INFO] WEB5St2Mb7=pass
[[19:16:05]] [SUCCESS] Screenshot refreshed successfully
[[19:16:05]] [SUCCESS] Screenshot refreshed successfully
[[19:16:05]] [INFO] WEB5St2Mb7=running
[[19:16:05]] [INFO] Executing action 531/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[19:16:04]] [SUCCESS] Screenshot refreshed
[[19:16:04]] [INFO] Refreshing screenshot...
[[19:16:04]] [INFO] To7bij5MnF=pass
[[19:15:59]] [INFO] To7bij5MnF=running
[[19:15:59]] [INFO] Executing action 530/576: Swipe from (5%, 50%) to (90%, 50%)
[[19:15:59]] [SUCCESS] Screenshot refreshed successfully
[[19:15:59]] [SUCCESS] Screenshot refreshed successfully
[[19:15:59]] [SUCCESS] Screenshot refreshed
[[19:15:59]] [INFO] Refreshing screenshot...
[[19:15:59]] [INFO] NkybTKfs2U=pass
[[19:15:53]] [SUCCESS] Screenshot refreshed successfully
[[19:15:53]] [SUCCESS] Screenshot refreshed successfully
[[19:15:52]] [INFO] NkybTKfs2U=running
[[19:15:52]] [INFO] Executing action 529/576: Swipe from (5%, 50%) to (90%, 50%)
[[19:15:52]] [SUCCESS] Screenshot refreshed
[[19:15:52]] [INFO] Refreshing screenshot...
[[19:15:52]] [INFO] dYEtjrv6lz=pass
[[19:15:48]] [SUCCESS] Screenshot refreshed successfully
[[19:15:48]] [SUCCESS] Screenshot refreshed successfully
[[19:15:48]] [INFO] dYEtjrv6lz=running
[[19:15:48]] [INFO] Executing action 528/576: Tap on Text: "Months"
[[19:15:47]] [SUCCESS] Screenshot refreshed
[[19:15:47]] [INFO] Refreshing screenshot...
[[19:15:47]] [INFO] eGQ7VrKUSo=pass
[[19:15:43]] [SUCCESS] Screenshot refreshed successfully
[[19:15:43]] [SUCCESS] Screenshot refreshed successfully
[[19:15:43]] [INFO] eGQ7VrKUSo=running
[[19:15:43]] [INFO] Executing action 527/576: Tap on Text: "Age"
[[19:15:42]] [SUCCESS] Screenshot refreshed
[[19:15:42]] [INFO] Refreshing screenshot...
[[19:15:42]] [INFO] zNRPvs2cC4=pass
[[19:15:38]] [SUCCESS] Screenshot refreshed successfully
[[19:15:38]] [SUCCESS] Screenshot refreshed successfully
[[19:15:38]] [INFO] zNRPvs2cC4=running
[[19:15:38]] [INFO] Executing action 526/576: Tap on Text: "Toys"
[[19:15:37]] [SUCCESS] Screenshot refreshed
[[19:15:37]] [INFO] Refreshing screenshot...
[[19:15:37]] [INFO] KyyS139agr=pass
[[19:15:33]] [SUCCESS] Screenshot refreshed successfully
[[19:15:33]] [SUCCESS] Screenshot refreshed successfully
[[19:15:32]] [INFO] KyyS139agr=running
[[19:15:32]] [INFO] Executing action 525/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[19:15:32]] [SUCCESS] Screenshot refreshed
[[19:15:32]] [INFO] Refreshing screenshot...
[[19:15:32]] [INFO] 5e4LeoW1YU=pass
[[19:15:28]] [SUCCESS] Screenshot refreshed successfully
[[19:15:28]] [SUCCESS] Screenshot refreshed successfully
[[19:15:27]] [INFO] 5e4LeoW1YU=running
[[19:15:27]] [INFO] Executing action 524/576: Restart app: env[appid]
[[19:15:27]] [SUCCESS] Screenshot refreshed
[[19:15:27]] [INFO] Refreshing screenshot...
[[19:15:26]] [SUCCESS] Screenshot refreshed
[[19:15:26]] [INFO] Refreshing screenshot...
[[19:15:09]] [SUCCESS] Screenshot refreshed successfully
[[19:15:09]] [SUCCESS] Screenshot refreshed successfully
[[19:15:09]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[19:15:08]] [SUCCESS] Screenshot refreshed
[[19:15:08]] [INFO] Refreshing screenshot...
[[19:14:22]] [SUCCESS] Screenshot refreshed successfully
[[19:14:22]] [SUCCESS] Screenshot refreshed successfully
[[19:14:22]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:14:22]] [SUCCESS] Screenshot refreshed
[[19:14:22]] [INFO] Refreshing screenshot...
[[19:14:06]] [SUCCESS] Screenshot refreshed successfully
[[19:14:06]] [SUCCESS] Screenshot refreshed successfully
[[19:14:06]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[19:14:05]] [SUCCESS] Screenshot refreshed
[[19:14:05]] [INFO] Refreshing screenshot...
[[19:13:21]] [SUCCESS] Screenshot refreshed successfully
[[19:13:21]] [SUCCESS] Screenshot refreshed successfully
[[19:13:20]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:13:20]] [SUCCESS] Screenshot refreshed
[[19:13:20]] [INFO] Refreshing screenshot...
[[19:13:04]] [SUCCESS] Screenshot refreshed successfully
[[19:13:04]] [SUCCESS] Screenshot refreshed successfully
[[19:13:04]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[19:13:03]] [SUCCESS] Screenshot refreshed
[[19:13:03]] [INFO] Refreshing screenshot...
[[19:12:19]] [SUCCESS] Screenshot refreshed successfully
[[19:12:19]] [SUCCESS] Screenshot refreshed successfully
[[19:12:18]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:12:18]] [SUCCESS] Screenshot refreshed
[[19:12:18]] [INFO] Refreshing screenshot...
[[19:12:02]] [SUCCESS] Screenshot refreshed successfully
[[19:12:02]] [SUCCESS] Screenshot refreshed successfully
[[19:12:02]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[19:12:01]] [SUCCESS] Screenshot refreshed
[[19:12:01]] [INFO] Refreshing screenshot...
[[19:11:17]] [SUCCESS] Screenshot refreshed successfully
[[19:11:17]] [SUCCESS] Screenshot refreshed successfully
[[19:11:16]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:11:16]] [SUCCESS] Screenshot refreshed
[[19:11:16]] [INFO] Refreshing screenshot...
[[19:10:57]] [SUCCESS] Screenshot refreshed successfully
[[19:10:57]] [SUCCESS] Screenshot refreshed successfully
[[19:10:56]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[19:10:56]] [SUCCESS] Screenshot refreshed
[[19:10:56]] [INFO] Refreshing screenshot...
[[19:10:09]] [SUCCESS] Screenshot refreshed successfully
[[19:10:09]] [SUCCESS] Screenshot refreshed successfully
[[19:10:08]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[19:10:08]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[19:10:08]] [INFO] Loading steps for Multi Step action: Click_Paginations
[[19:10:08]] [INFO] aqs7O0Yq2p=running
[[19:10:08]] [INFO] Executing action 523/576: Execute Test Case: Click_Paginations (10 steps)
[[19:10:08]] [SUCCESS] Screenshot refreshed
[[19:10:08]] [INFO] Refreshing screenshot...
[[19:10:08]] [INFO] IL6kON0uQ9=pass
[[19:10:03]] [SUCCESS] Screenshot refreshed successfully
[[19:10:03]] [SUCCESS] Screenshot refreshed successfully
[[19:10:03]] [INFO] IL6kON0uQ9=running
[[19:10:03]] [INFO] Executing action 522/576: iOS Function: text - Text: "kids toys"
[[19:10:02]] [SUCCESS] Screenshot refreshed
[[19:10:02]] [INFO] Refreshing screenshot...
[[19:10:02]] [INFO] 6G6P3UE7Uy=pass
[[19:09:57]] [SUCCESS] Screenshot refreshed successfully
[[19:09:57]] [SUCCESS] Screenshot refreshed successfully
[[19:09:56]] [INFO] 6G6P3UE7Uy=running
[[19:09:56]] [INFO] Executing action 521/576: Tap on Text: "Find"
[[19:09:56]] [SUCCESS] Screenshot refreshed
[[19:09:56]] [INFO] Refreshing screenshot...
[[19:09:56]] [INFO] 7xs3GiydGF=pass
[[19:09:52]] [SUCCESS] Screenshot refreshed successfully
[[19:09:52]] [SUCCESS] Screenshot refreshed successfully
[[19:09:52]] [INFO] 7xs3GiydGF=running
[[19:09:52]] [INFO] Executing action 520/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[19:09:51]] [SUCCESS] Screenshot refreshed
[[19:09:51]] [INFO] Refreshing screenshot...
[[19:09:51]] [INFO] VqSa9z9R2Q=pass
[[19:09:49]] [INFO] VqSa9z9R2Q=running
[[19:09:49]] [INFO] Executing action 519/576: Launch app: env[appid]
[[19:09:49]] [SUCCESS] Screenshot refreshed successfully
[[19:09:49]] [SUCCESS] Screenshot refreshed successfully
[[19:09:49]] [SUCCESS] Screenshot refreshed
[[19:09:49]] [INFO] Refreshing screenshot...
[[19:09:49]] [INFO] RHEU77LRMw=pass
[[19:09:45]] [SUCCESS] Screenshot refreshed successfully
[[19:09:45]] [SUCCESS] Screenshot refreshed successfully
[[19:09:45]] [INFO] RHEU77LRMw=running
[[19:09:45]] [INFO] Executing action 518/576: Tap on Text: "+61"
[[19:09:44]] [SUCCESS] Screenshot refreshed
[[19:09:44]] [INFO] Refreshing screenshot...
[[19:09:44]] [INFO] MTRbUlaRvI=pass
[[19:09:40]] [SUCCESS] Screenshot refreshed successfully
[[19:09:40]] [SUCCESS] Screenshot refreshed successfully
[[19:09:40]] [INFO] MTRbUlaRvI=running
[[19:09:40]] [INFO] Executing action 517/576: Tap on Text: "1800"
[[19:09:39]] [SUCCESS] Screenshot refreshed
[[19:09:39]] [INFO] Refreshing screenshot...
[[19:09:39]] [INFO] I0tM87Yjhc=pass
[[19:09:34]] [SUCCESS] Screenshot refreshed successfully
[[19:09:34]] [SUCCESS] Screenshot refreshed successfully
[[19:09:34]] [INFO] I0tM87Yjhc=running
[[19:09:34]] [INFO] Executing action 516/576: Tap on Text: "click"
[[19:09:33]] [SUCCESS] Screenshot refreshed
[[19:09:33]] [INFO] Refreshing screenshot...
[[19:09:33]] [INFO] t6L5vWfBYM=pass
[[19:09:14]] [SUCCESS] Screenshot refreshed successfully
[[19:09:14]] [SUCCESS] Screenshot refreshed successfully
[[19:09:14]] [INFO] t6L5vWfBYM=running
[[19:09:14]] [INFO] Executing action 515/576: Swipe from (50%, 70%) to (50%, 30%)
[[19:09:13]] [SUCCESS] Screenshot refreshed
[[19:09:13]] [INFO] Refreshing screenshot...
[[19:09:13]] [INFO] DhFJzlme9K=pass
[[19:09:09]] [INFO] DhFJzlme9K=running
[[19:09:09]] [INFO] Executing action 514/576: Tap on Text: "FAQ"
[[19:09:09]] [SUCCESS] Screenshot refreshed successfully
[[19:09:09]] [SUCCESS] Screenshot refreshed successfully
[[19:09:08]] [SUCCESS] Screenshot refreshed
[[19:09:08]] [INFO] Refreshing screenshot...
[[19:09:08]] [INFO] g17Boaefhg=pass
[[19:09:03]] [SUCCESS] Screenshot refreshed successfully
[[19:09:03]] [SUCCESS] Screenshot refreshed successfully
[[19:09:03]] [INFO] g17Boaefhg=running
[[19:09:03]] [INFO] Executing action 513/576: Tap on Text: "Help"
[[19:09:03]] [SUCCESS] Screenshot refreshed
[[19:09:03]] [INFO] Refreshing screenshot...
[[19:09:03]] [INFO] SqDiBhmyOG=pass
[[19:08:59]] [SUCCESS] Screenshot refreshed successfully
[[19:08:59]] [SUCCESS] Screenshot refreshed successfully
[[19:08:58]] [INFO] SqDiBhmyOG=running
[[19:08:58]] [INFO] Executing action 512/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:08:58]] [SUCCESS] Screenshot refreshed
[[19:08:58]] [INFO] Refreshing screenshot...
[[19:08:58]] [INFO] OR0SKKnFxy=pass
[[19:08:44]] [SUCCESS] Screenshot refreshed successfully
[[19:08:44]] [SUCCESS] Screenshot refreshed successfully
[[19:08:44]] [INFO] OR0SKKnFxy=running
[[19:08:44]] [INFO] Executing action 511/576: Restart app: env[appid]
[[19:08:43]] [SUCCESS] Screenshot refreshed
[[19:08:43]] [INFO] Refreshing screenshot...
[[19:08:43]] [INFO] kPdSiomhwu=pass
[[19:08:17]] [SUCCESS] Screenshot refreshed successfully
[[19:08:17]] [SUCCESS] Screenshot refreshed successfully
[[19:08:17]] [INFO] kPdSiomhwu=running
[[19:08:17]] [INFO] Executing action 510/576: cleanupSteps action
[[19:08:16]] [SUCCESS] Screenshot refreshed
[[19:08:16]] [INFO] Refreshing screenshot...
[[19:08:16]] [INFO] Qb1AArnpCH=pass
[[19:08:10]] [SUCCESS] Screenshot refreshed successfully
[[19:08:10]] [SUCCESS] Screenshot refreshed successfully
[[19:08:09]] [INFO] Qb1AArnpCH=running
[[19:08:09]] [INFO] Executing action 509/576: Wait for 5 ms
[[19:08:09]] [SUCCESS] Screenshot refreshed
[[19:08:09]] [INFO] Refreshing screenshot...
[[19:08:09]] [INFO] 0SHxVJkq0l=pass
[[19:07:47]] [INFO] 0SHxVJkq0l=running
[[19:07:47]] [INFO] Executing action 508/576: If exists: id="//XCUIElementTypeButton[contains(@name,"Remove")]" (timeout: 20s) → Then tap at (0, 0)
[[19:07:47]] [SUCCESS] Screenshot refreshed successfully
[[19:07:47]] [SUCCESS] Screenshot refreshed successfully
[[19:07:46]] [SUCCESS] Screenshot refreshed
[[19:07:46]] [INFO] Refreshing screenshot...
[[19:07:46]] [INFO] UpUSVInizv=pass
[[19:07:43]] [SUCCESS] Screenshot refreshed successfully
[[19:07:43]] [SUCCESS] Screenshot refreshed successfully
[[19:07:43]] [INFO] UpUSVInizv=running
[[19:07:43]] [INFO] Executing action 507/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[19:07:42]] [SUCCESS] Screenshot refreshed
[[19:07:42]] [INFO] Refreshing screenshot...
[[19:07:42]] [INFO] c4T3INQkzn=pass
[[19:07:37]] [SUCCESS] Screenshot refreshed successfully
[[19:07:37]] [SUCCESS] Screenshot refreshed successfully
[[19:07:36]] [INFO] c4T3INQkzn=running
[[19:07:36]] [INFO] Executing action 506/576: Restart app: env[appid]
[[19:07:36]] [SUCCESS] Screenshot refreshed
[[19:07:36]] [INFO] Refreshing screenshot...
[[19:07:36]] [INFO] Cr1z26u7Va=pass
[[19:07:30]] [SUCCESS] Screenshot refreshed successfully
[[19:07:30]] [SUCCESS] Screenshot refreshed successfully
[[19:07:29]] [INFO] Cr1z26u7Va=running
[[19:07:29]] [INFO] Executing action 505/576: If exists: accessibility_id="Add to bag" (timeout: 15s) → Then tap at (0, 0)
[[19:07:29]] [SUCCESS] Screenshot refreshed
[[19:07:29]] [INFO] Refreshing screenshot...
[[19:07:29]] [INFO] 2hGhWulI52=pass
[[19:07:27]] [SUCCESS] Screenshot refreshed successfully
[[19:07:27]] [SUCCESS] Screenshot refreshed successfully
[[19:07:25]] [INFO] 2hGhWulI52=running
[[19:07:25]] [INFO] Executing action 504/576: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[19:07:25]] [SUCCESS] Screenshot refreshed
[[19:07:25]] [INFO] Refreshing screenshot...
[[19:07:25]] [INFO] n57KEWjTea=pass
[[19:07:20]] [SUCCESS] Screenshot refreshed successfully
[[19:07:20]] [SUCCESS] Screenshot refreshed successfully
[[19:07:19]] [INFO] n57KEWjTea=running
[[19:07:19]] [INFO] Executing action 503/576: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[19:07:19]] [SUCCESS] Screenshot refreshed
[[19:07:19]] [INFO] Refreshing screenshot...
[[19:07:19]] [INFO] L59V5hqMX9=pass
[[19:07:15]] [SUCCESS] Screenshot refreshed successfully
[[19:07:15]] [SUCCESS] Screenshot refreshed successfully
[[19:07:14]] [INFO] L59V5hqMX9=running
[[19:07:14]] [INFO] Executing action 502/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[19:07:14]] [SUCCESS] Screenshot refreshed
[[19:07:14]] [INFO] Refreshing screenshot...
[[19:07:14]] [INFO] OKiI82VdnE=pass
[[19:07:08]] [SUCCESS] Screenshot refreshed successfully
[[19:07:08]] [SUCCESS] Screenshot refreshed successfully
[[19:07:07]] [INFO] OKiI82VdnE=running
[[19:07:07]] [INFO] Executing action 501/576: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[19:07:06]] [SUCCESS] Screenshot refreshed
[[19:07:06]] [INFO] Refreshing screenshot...
[[19:07:06]] [INFO] 3KNqlNy6Bj=pass
[[19:07:02]] [SUCCESS] Screenshot refreshed successfully
[[19:07:02]] [SUCCESS] Screenshot refreshed successfully
[[19:07:02]] [INFO] 3KNqlNy6Bj=running
[[19:07:02]] [INFO] Executing action 500/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[19:07:01]] [SUCCESS] Screenshot refreshed
[[19:07:01]] [INFO] Refreshing screenshot...
[[19:07:01]] [INFO] 3NOS1fbxZs=pass
[[19:06:57]] [SUCCESS] Screenshot refreshed successfully
[[19:06:57]] [SUCCESS] Screenshot refreshed successfully
[[19:06:57]] [INFO] 3NOS1fbxZs=running
[[19:06:57]] [INFO] Executing action 499/576: Tap on image: banner-close-updated.png
[[19:06:56]] [SUCCESS] Screenshot refreshed
[[19:06:56]] [INFO] Refreshing screenshot...
[[19:06:56]] [INFO] K0c1gL9UK1=pass
[[19:06:52]] [SUCCESS] Screenshot refreshed successfully
[[19:06:52]] [SUCCESS] Screenshot refreshed successfully
[[19:06:52]] [INFO] K0c1gL9UK1=running
[[19:06:52]] [INFO] Executing action 498/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[19:06:52]] [SUCCESS] Screenshot refreshed
[[19:06:52]] [INFO] Refreshing screenshot...
[[19:06:52]] [INFO] IW6uAwdtiW=pass
[[19:06:48]] [SUCCESS] Screenshot refreshed successfully
[[19:06:48]] [SUCCESS] Screenshot refreshed successfully
[[19:06:48]] [INFO] IW6uAwdtiW=running
[[19:06:48]] [INFO] Executing action 497/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[19:06:47]] [SUCCESS] Screenshot refreshed
[[19:06:47]] [INFO] Refreshing screenshot...
[[19:06:47]] [INFO] DbM0d0m6rU=pass
[[19:06:43]] [INFO] DbM0d0m6rU=running
[[19:06:43]] [INFO] Executing action 496/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[19:06:43]] [SUCCESS] Screenshot refreshed successfully
[[19:06:43]] [SUCCESS] Screenshot refreshed successfully
[[19:06:42]] [SUCCESS] Screenshot refreshed
[[19:06:42]] [INFO] Refreshing screenshot...
[[19:06:42]] [INFO] UpUSVInizv=pass
[[19:06:39]] [SUCCESS] Screenshot refreshed successfully
[[19:06:39]] [SUCCESS] Screenshot refreshed successfully
[[19:06:38]] [INFO] UpUSVInizv=running
[[19:06:38]] [INFO] Executing action 495/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[19:06:38]] [SUCCESS] Screenshot refreshed
[[19:06:38]] [INFO] Refreshing screenshot...
[[19:06:38]] [INFO] Iab9zCfpqO=pass
[[19:06:30]] [SUCCESS] Screenshot refreshed successfully
[[19:06:30]] [SUCCESS] Screenshot refreshed successfully
[[19:06:30]] [INFO] Iab9zCfpqO=running
[[19:06:30]] [INFO] Executing action 494/576: Tap on element with accessibility_id: Add to bag
[[19:06:29]] [SUCCESS] Screenshot refreshed
[[19:06:29]] [INFO] Refreshing screenshot...
[[19:06:29]] [INFO] Qy0Y0uJchm=pass
[[19:06:25]] [SUCCESS] Screenshot refreshed successfully
[[19:06:25]] [SUCCESS] Screenshot refreshed successfully
[[19:06:25]] [INFO] Qy0Y0uJchm=running
[[19:06:25]] [INFO] Executing action 493/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[19:06:24]] [SUCCESS] Screenshot refreshed
[[19:06:24]] [INFO] Refreshing screenshot...
[[19:06:24]] [INFO] YHaMIjULRf=pass
[[19:06:19]] [SUCCESS] Screenshot refreshed successfully
[[19:06:19]] [SUCCESS] Screenshot refreshed successfully
[[19:06:18]] [INFO] YHaMIjULRf=running
[[19:06:18]] [INFO] Executing action 492/576: Tap on Text: "List"
[[19:06:17]] [SUCCESS] Screenshot refreshed
[[19:06:17]] [INFO] Refreshing screenshot...
[[19:06:17]] [INFO] igReeDqips=pass
[[19:06:13]] [SUCCESS] Screenshot refreshed successfully
[[19:06:13]] [SUCCESS] Screenshot refreshed successfully
[[19:06:12]] [INFO] igReeDqips=running
[[19:06:12]] [INFO] Executing action 491/576: Tap on image: env[catalogue-menu-img]
[[19:06:12]] [SUCCESS] Screenshot refreshed
[[19:06:12]] [INFO] Refreshing screenshot...
[[19:06:12]] [INFO] Xqj9EIVE7g=pass
[[19:05:50]] [SUCCESS] Screenshot refreshed successfully
[[19:05:50]] [SUCCESS] Screenshot refreshed successfully
[[19:05:49]] [INFO] Xqj9EIVE7g=running
[[19:05:49]] [INFO] Executing action 490/576: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[19:05:48]] [SUCCESS] Screenshot refreshed
[[19:05:48]] [INFO] Refreshing screenshot...
[[19:05:48]] [INFO] gkkQzTCmma=pass
[[19:05:43]] [SUCCESS] Screenshot refreshed successfully
[[19:05:43]] [SUCCESS] Screenshot refreshed successfully
[[19:05:43]] [INFO] gkkQzTCmma=running
[[19:05:43]] [INFO] Executing action 489/576: Tap on Text: "Catalogue"
[[19:05:42]] [SUCCESS] Screenshot refreshed
[[19:05:42]] [INFO] Refreshing screenshot...
[[19:05:42]] [INFO] UpUSVInizv=pass
[[19:05:39]] [SUCCESS] Screenshot refreshed successfully
[[19:05:39]] [SUCCESS] Screenshot refreshed successfully
[[19:05:38]] [INFO] UpUSVInizv=running
[[19:05:38]] [INFO] Executing action 488/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[19:05:37]] [SUCCESS] Screenshot refreshed
[[19:05:37]] [INFO] Refreshing screenshot...
[[19:05:37]] [INFO] Cmvm82hiAa=pass
[[19:05:30]] [SUCCESS] Screenshot refreshed successfully
[[19:05:30]] [SUCCESS] Screenshot refreshed successfully
[[19:05:30]] [INFO] Cmvm82hiAa=running
[[19:05:30]] [INFO] Executing action 487/576: Tap on element with accessibility_id: Add to bag
[[19:05:29]] [SUCCESS] Screenshot refreshed
[[19:05:29]] [INFO] Refreshing screenshot...
[[19:05:29]] [INFO] JcAR0JctQ6=pass
[[19:05:26]] [SUCCESS] Screenshot refreshed successfully
[[19:05:26]] [SUCCESS] Screenshot refreshed successfully
[[19:05:25]] [INFO] JcAR0JctQ6=running
[[19:05:25]] [INFO] Executing action 486/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[19:05:25]] [SUCCESS] Screenshot refreshed
[[19:05:25]] [INFO] Refreshing screenshot...
[[19:05:25]] [INFO] Pd7cReoJM6=pass
[[19:05:19]] [SUCCESS] Screenshot refreshed successfully
[[19:05:19]] [SUCCESS] Screenshot refreshed successfully
[[19:05:19]] [INFO] Pd7cReoJM6=running
[[19:05:19]] [INFO] Executing action 485/576: Tap on Text: "List"
[[19:05:18]] [SUCCESS] Screenshot refreshed
[[19:05:18]] [INFO] Refreshing screenshot...
[[19:05:18]] [INFO] igReeDqips=pass
[[19:05:14]] [SUCCESS] Screenshot refreshed successfully
[[19:05:14]] [SUCCESS] Screenshot refreshed successfully
[[19:05:13]] [INFO] igReeDqips=running
[[19:05:13]] [INFO] Executing action 484/576: Tap on image: env[catalogue-menu-img]
[[19:05:12]] [SUCCESS] Screenshot refreshed
[[19:05:12]] [INFO] Refreshing screenshot...
[[19:05:12]] [INFO] Xqj9EIVE7g=pass
[[19:04:49]] [SUCCESS] Screenshot refreshed successfully
[[19:04:49]] [SUCCESS] Screenshot refreshed successfully
[[19:04:48]] [INFO] Xqj9EIVE7g=running
[[19:04:48]] [INFO] Executing action 483/576: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[19:04:48]] [SUCCESS] Screenshot refreshed
[[19:04:48]] [INFO] Refreshing screenshot...
[[19:04:48]] [INFO] gkkQzTCmma=pass
[[19:04:43]] [SUCCESS] Screenshot refreshed successfully
[[19:04:43]] [SUCCESS] Screenshot refreshed successfully
[[19:04:42]] [INFO] gkkQzTCmma=running
[[19:04:42]] [INFO] Executing action 482/576: Tap on Text: "Catalogue"
[[19:04:42]] [SUCCESS] Screenshot refreshed
[[19:04:42]] [INFO] Refreshing screenshot...
[[19:04:42]] [INFO] UpUSVInizv=pass
[[19:04:38]] [SUCCESS] Screenshot refreshed successfully
[[19:04:38]] [SUCCESS] Screenshot refreshed successfully
[[19:04:38]] [INFO] UpUSVInizv=running
[[19:04:38]] [INFO] Executing action 481/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[19:04:37]] [SUCCESS] Screenshot refreshed
[[19:04:37]] [INFO] Refreshing screenshot...
[[19:04:37]] [INFO] 0QtNHB5WEK=pass
[[19:04:34]] [SUCCESS] Screenshot refreshed successfully
[[19:04:34]] [SUCCESS] Screenshot refreshed successfully
[[19:04:34]] [INFO] 0QtNHB5WEK=running
[[19:04:34]] [INFO] Executing action 480/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[19:04:33]] [SUCCESS] Screenshot refreshed
[[19:04:33]] [INFO] Refreshing screenshot...
[[19:04:33]] [INFO] fTdGMJ3NH3=pass
[[19:04:30]] [SUCCESS] Screenshot refreshed successfully
[[19:04:30]] [SUCCESS] Screenshot refreshed successfully
[[19:04:30]] [INFO] fTdGMJ3NH3=running
[[19:04:30]] [INFO] Executing action 479/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[19:04:30]] [SUCCESS] Screenshot refreshed
[[19:04:30]] [INFO] Refreshing screenshot...
[[19:04:30]] [INFO] rYJcLPh8Aq=pass
[[19:04:26]] [INFO] rYJcLPh8Aq=running
[[19:04:26]] [INFO] Executing action 478/576: iOS Function: text - Text: "kmart au"
[[19:04:26]] [SUCCESS] Screenshot refreshed successfully
[[19:04:26]] [SUCCESS] Screenshot refreshed successfully
[[19:04:26]] [SUCCESS] Screenshot refreshed
[[19:04:26]] [INFO] Refreshing screenshot...
[[19:04:26]] [INFO] 0Q0fm6OTij=pass
[[19:04:23]] [SUCCESS] Screenshot refreshed successfully
[[19:04:23]] [SUCCESS] Screenshot refreshed successfully
[[19:04:23]] [INFO] 0Q0fm6OTij=running
[[19:04:23]] [INFO] Executing action 477/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[19:04:23]] [SUCCESS] Screenshot refreshed
[[19:04:23]] [INFO] Refreshing screenshot...
[[19:04:23]] [INFO] xVuuejtCFA=pass
[[19:04:18]] [SUCCESS] Screenshot refreshed successfully
[[19:04:18]] [SUCCESS] Screenshot refreshed successfully
[[19:04:18]] [INFO] xVuuejtCFA=running
[[19:04:18]] [INFO] Executing action 476/576: Restart app: com.apple.mobilesafari
[[19:04:17]] [SUCCESS] Screenshot refreshed
[[19:04:17]] [INFO] Refreshing screenshot...
[[19:04:17]] [INFO] LcYLwUffqj=pass
[[19:04:12]] [SUCCESS] Screenshot refreshed successfully
[[19:04:12]] [SUCCESS] Screenshot refreshed successfully
[[19:04:12]] [INFO] LcYLwUffqj=running
[[19:04:12]] [INFO] Executing action 475/576: Tap on Text: "out"
[[19:04:12]] [SUCCESS] Screenshot refreshed
[[19:04:12]] [INFO] Refreshing screenshot...
[[19:04:12]] [INFO] ZZPNqTJ65s=pass
[[19:04:08]] [SUCCESS] Screenshot refreshed successfully
[[19:04:08]] [SUCCESS] Screenshot refreshed successfully
[[19:04:08]] [INFO] ZZPNqTJ65s=running
[[19:04:08]] [INFO] Executing action 474/576: Swipe from (50%, 70%) to (50%, 30%)
[[19:04:07]] [SUCCESS] Screenshot refreshed
[[19:04:07]] [INFO] Refreshing screenshot...
[[19:04:07]] [INFO] UpUSVInizv=pass
[[19:04:04]] [SUCCESS] Screenshot refreshed successfully
[[19:04:04]] [SUCCESS] Screenshot refreshed successfully
[[19:04:03]] [INFO] UpUSVInizv=running
[[19:04:03]] [INFO] Executing action 473/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[19:04:03]] [SUCCESS] Screenshot refreshed
[[19:04:03]] [INFO] Refreshing screenshot...
[[19:04:03]] [INFO] hCCEvRtj1A=pass
[[19:03:57]] [INFO] hCCEvRtj1A=running
[[19:03:57]] [INFO] Executing action 472/576: Restart app: env[appid]
[[19:03:57]] [SUCCESS] Screenshot refreshed successfully
[[19:03:57]] [SUCCESS] Screenshot refreshed successfully
[[19:03:57]] [SUCCESS] Screenshot refreshed
[[19:03:57]] [INFO] Refreshing screenshot...
[[19:03:57]] [INFO] V42eHtTRYW=pass
[[19:03:50]] [INFO] V42eHtTRYW=running
[[19:03:50]] [INFO] Executing action 471/576: Wait for 5 ms
[[19:03:50]] [SUCCESS] Screenshot refreshed successfully
[[19:03:50]] [SUCCESS] Screenshot refreshed successfully
[[19:03:50]] [SUCCESS] Screenshot refreshed
[[19:03:50]] [INFO] Refreshing screenshot...
[[19:03:50]] [INFO] GRwHMVK4sA=pass
[[19:03:48]] [INFO] GRwHMVK4sA=running
[[19:03:48]] [INFO] Executing action 470/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[19:03:48]] [SUCCESS] Screenshot refreshed successfully
[[19:03:48]] [SUCCESS] Screenshot refreshed successfully
[[19:03:47]] [SUCCESS] Screenshot refreshed
[[19:03:47]] [INFO] Refreshing screenshot...
[[19:03:47]] [INFO] V42eHtTRYW=pass
[[19:03:41]] [INFO] V42eHtTRYW=running
[[19:03:41]] [INFO] Executing action 469/576: Wait for 5 ms
[[19:03:41]] [SUCCESS] Screenshot refreshed successfully
[[19:03:41]] [SUCCESS] Screenshot refreshed successfully
[[19:03:40]] [SUCCESS] Screenshot refreshed
[[19:03:40]] [INFO] Refreshing screenshot...
[[19:03:40]] [INFO] LfyQctrEJn=pass
[[19:03:39]] [SUCCESS] Screenshot refreshed successfully
[[19:03:39]] [SUCCESS] Screenshot refreshed successfully
[[19:03:39]] [INFO] LfyQctrEJn=running
[[19:03:39]] [INFO] Executing action 468/576: Launch app: com.apple.Preferences
[[19:03:38]] [SUCCESS] Screenshot refreshed
[[19:03:38]] [INFO] Refreshing screenshot...
[[19:03:38]] [INFO] seQcUKjkSU=pass
[[19:03:36]] [SUCCESS] Screenshot refreshed successfully
[[19:03:36]] [SUCCESS] Screenshot refreshed successfully
[[19:03:36]] [INFO] seQcUKjkSU=running
[[19:03:36]] [INFO] Executing action 467/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:03:36]] [SUCCESS] Screenshot refreshed
[[19:03:36]] [INFO] Refreshing screenshot...
[[19:03:36]] [INFO] UpUSVInizv=pass
[[19:03:34]] [SUCCESS] Screenshot refreshed successfully
[[19:03:34]] [SUCCESS] Screenshot refreshed successfully
[[19:03:34]] [INFO] UpUSVInizv=running
[[19:03:34]] [INFO] Executing action 466/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[19:03:33]] [SUCCESS] Screenshot refreshed
[[19:03:33]] [INFO] Refreshing screenshot...
[[19:03:33]] [INFO] WoymrHdtrO=pass
[[19:03:31]] [SUCCESS] Screenshot refreshed successfully
[[19:03:31]] [SUCCESS] Screenshot refreshed successfully
[[19:03:31]] [INFO] WoymrHdtrO=running
[[19:03:31]] [INFO] Executing action 465/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:03:31]] [SUCCESS] Screenshot refreshed
[[19:03:31]] [INFO] Refreshing screenshot...
[[19:03:31]] [INFO] 6xgrAWyfZ4=pass
[[19:03:29]] [SUCCESS] Screenshot refreshed successfully
[[19:03:29]] [SUCCESS] Screenshot refreshed successfully
[[19:03:29]] [INFO] 6xgrAWyfZ4=running
[[19:03:29]] [INFO] Executing action 464/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[19:03:28]] [SUCCESS] Screenshot refreshed
[[19:03:28]] [INFO] Refreshing screenshot...
[[19:03:28]] [INFO] eSr9EFlJek=pass
[[19:03:26]] [SUCCESS] Screenshot refreshed successfully
[[19:03:26]] [SUCCESS] Screenshot refreshed successfully
[[19:03:26]] [INFO] eSr9EFlJek=running
[[19:03:26]] [INFO] Executing action 463/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:03:26]] [SUCCESS] Screenshot refreshed
[[19:03:26]] [INFO] Refreshing screenshot...
[[19:03:26]] [INFO] 3KNqlNy6Bj=pass
[[19:03:24]] [SUCCESS] Screenshot refreshed successfully
[[19:03:24]] [SUCCESS] Screenshot refreshed successfully
[[19:03:23]] [INFO] 3KNqlNy6Bj=running
[[19:03:23]] [INFO] Executing action 462/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[19:03:23]] [SUCCESS] Screenshot refreshed
[[19:03:23]] [INFO] Refreshing screenshot...
[[19:03:23]] [INFO] cokvFXhj4c=pass
[[19:03:21]] [SUCCESS] Screenshot refreshed successfully
[[19:03:21]] [SUCCESS] Screenshot refreshed successfully
[[19:03:21]] [INFO] cokvFXhj4c=running
[[19:03:21]] [INFO] Executing action 461/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[19:03:21]] [SUCCESS] Screenshot refreshed
[[19:03:21]] [INFO] Refreshing screenshot...
[[19:03:21]] [INFO] oSQ8sPdVOJ=pass
[[19:03:16]] [INFO] oSQ8sPdVOJ=running
[[19:03:16]] [INFO] Executing action 460/576: Restart app: env[appid]
[[19:03:15]] [SUCCESS] Screenshot refreshed successfully
[[19:03:15]] [SUCCESS] Screenshot refreshed successfully
[[19:03:15]] [SUCCESS] Screenshot refreshed
[[19:03:15]] [INFO] Refreshing screenshot...
[[19:03:15]] [INFO] V42eHtTRYW=pass
[[19:03:09]] [INFO] V42eHtTRYW=running
[[19:03:09]] [INFO] Executing action 459/576: Wait for 5 ms
[[19:03:09]] [SUCCESS] Screenshot refreshed successfully
[[19:03:09]] [SUCCESS] Screenshot refreshed successfully
[[19:03:08]] [SUCCESS] Screenshot refreshed
[[19:03:08]] [INFO] Refreshing screenshot...
[[19:03:08]] [INFO] jUCAk6GJc4=pass
[[19:03:05]] [INFO] jUCAk6GJc4=running
[[19:03:05]] [INFO] Executing action 458/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[19:03:05]] [SUCCESS] Screenshot refreshed successfully
[[19:03:05]] [SUCCESS] Screenshot refreshed successfully
[[19:03:05]] [SUCCESS] Screenshot refreshed
[[19:03:05]] [INFO] Refreshing screenshot...
[[19:03:05]] [INFO] V42eHtTRYW=pass
[[19:02:58]] [INFO] V42eHtTRYW=running
[[19:02:58]] [INFO] Executing action 457/576: Wait for 5 ms
[[19:02:58]] [SUCCESS] Screenshot refreshed successfully
[[19:02:58]] [SUCCESS] Screenshot refreshed successfully
[[19:02:58]] [SUCCESS] Screenshot refreshed
[[19:02:58]] [INFO] Refreshing screenshot...
[[19:02:58]] [INFO] w1RV76df9x=pass
[[19:02:54]] [INFO] w1RV76df9x=running
[[19:02:54]] [INFO] Executing action 456/576: Tap on Text: "Wi-Fi"
[[19:02:53]] [SUCCESS] Screenshot refreshed successfully
[[19:02:53]] [SUCCESS] Screenshot refreshed successfully
[[19:02:53]] [SUCCESS] Screenshot refreshed
[[19:02:53]] [INFO] Refreshing screenshot...
[[19:02:53]] [INFO] LfyQctrEJn=pass
[[19:02:51]] [SUCCESS] Screenshot refreshed successfully
[[19:02:51]] [SUCCESS] Screenshot refreshed successfully
[[19:02:50]] [INFO] LfyQctrEJn=running
[[19:02:50]] [INFO] Executing action 455/576: Launch app: com.apple.Preferences
[[19:02:50]] [SUCCESS] Screenshot refreshed
[[19:02:50]] [INFO] Refreshing screenshot...
[[19:02:50]] [INFO] mIKA85kXaW=pass
[[19:02:49]] [SUCCESS] Screenshot refreshed successfully
[[19:02:49]] [SUCCESS] Screenshot refreshed successfully
[[19:02:47]] [INFO] mIKA85kXaW=running
[[19:02:47]] [INFO] Executing action 454/576: Terminate app: com.apple.Preferences
[[19:02:47]] [SUCCESS] Screenshot refreshed
[[19:02:47]] [INFO] Refreshing screenshot...
[[19:02:46]] [SUCCESS] Screenshot refreshed
[[19:02:46]] [INFO] Refreshing screenshot...
[[19:02:41]] [SUCCESS] Screenshot refreshed successfully
[[19:02:41]] [SUCCESS] Screenshot refreshed successfully
[[19:02:41]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[19:02:41]] [SUCCESS] Screenshot refreshed
[[19:02:41]] [INFO] Refreshing screenshot...
[[19:02:36]] [SUCCESS] Screenshot refreshed successfully
[[19:02:36]] [SUCCESS] Screenshot refreshed successfully
[[19:02:36]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[19:02:35]] [SUCCESS] Screenshot refreshed
[[19:02:35]] [INFO] Refreshing screenshot...
[[19:02:30]] [SUCCESS] Screenshot refreshed successfully
[[19:02:30]] [SUCCESS] Screenshot refreshed successfully
[[19:02:30]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[19:02:30]] [SUCCESS] Screenshot refreshed
[[19:02:30]] [INFO] Refreshing screenshot...
[[19:02:26]] [SUCCESS] Screenshot refreshed successfully
[[19:02:26]] [SUCCESS] Screenshot refreshed successfully
[[19:02:26]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[19:02:25]] [SUCCESS] Screenshot refreshed
[[19:02:25]] [INFO] Refreshing screenshot...
[[19:02:20]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[19:02:19]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[19:02:19]] [SUCCESS] Screenshot refreshed successfully
[[19:02:19]] [SUCCESS] Screenshot refreshed successfully
[[19:02:19]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[19:02:19]] [INFO] x6vffndoRV=running
[[19:02:19]] [INFO] Executing action 453/576: Execute Test Case: Kmart-Signin (6 steps)
[[19:02:19]] [SUCCESS] Screenshot refreshed
[[19:02:19]] [INFO] Refreshing screenshot...
[[19:02:19]] [INFO] rJ86z4njuR=pass
[[19:02:16]] [SUCCESS] Screenshot refreshed successfully
[[19:02:16]] [SUCCESS] Screenshot refreshed successfully
[[19:02:16]] [INFO] rJ86z4njuR=running
[[19:02:16]] [INFO] Executing action 452/576: iOS Function: alert_accept
[[19:02:15]] [SUCCESS] Screenshot refreshed
[[19:02:15]] [INFO] Refreshing screenshot...
[[19:02:15]] [INFO] veukWo4573=pass
[[19:02:11]] [SUCCESS] Screenshot refreshed successfully
[[19:02:11]] [SUCCESS] Screenshot refreshed successfully
[[19:02:11]] [INFO] veukWo4573=running
[[19:02:11]] [INFO] Executing action 451/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[19:02:10]] [SUCCESS] Screenshot refreshed
[[19:02:10]] [INFO] Refreshing screenshot...
[[19:02:10]] [INFO] XEbZHdi0GT=pass
[[19:01:57]] [SUCCESS] Screenshot refreshed successfully
[[19:01:57]] [SUCCESS] Screenshot refreshed successfully
[[19:01:56]] [INFO] XEbZHdi0GT=running
[[19:01:56]] [INFO] Executing action 450/576: Restart app: env[appid]
[[19:01:56]] [SUCCESS] Screenshot refreshed
[[19:01:56]] [INFO] Refreshing screenshot...
[[19:01:56]] [INFO] ubySifeF65=pass
[[19:01:29]] [SUCCESS] Screenshot refreshed successfully
[[19:01:29]] [SUCCESS] Screenshot refreshed successfully
[[19:01:28]] [INFO] ubySifeF65=running
[[19:01:28]] [INFO] Executing action 449/576: cleanupSteps action
[[19:01:28]] [SUCCESS] Screenshot refreshed
[[19:01:28]] [INFO] Refreshing screenshot...
[[19:01:28]] [INFO] xyHVihJMBi=pass
[[19:01:23]] [SUCCESS] Screenshot refreshed successfully
[[19:01:23]] [SUCCESS] Screenshot refreshed successfully
[[19:01:23]] [INFO] xyHVihJMBi=running
[[19:01:23]] [INFO] Executing action 448/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[19:01:23]] [SUCCESS] Screenshot refreshed
[[19:01:23]] [INFO] Refreshing screenshot...
[[19:01:23]] [INFO] mWeLQtXiL6=pass
[[19:01:16]] [SUCCESS] Screenshot refreshed successfully
[[19:01:16]] [SUCCESS] Screenshot refreshed successfully
[[19:01:16]] [INFO] mWeLQtXiL6=running
[[19:01:16]] [INFO] Executing action 447/576: Swipe from (50%, 70%) to (50%, 30%)
[[19:01:15]] [SUCCESS] Screenshot refreshed
[[19:01:15]] [INFO] Refreshing screenshot...
[[19:01:15]] [INFO] F4NGh9HrLw=pass
[[19:01:11]] [SUCCESS] Screenshot refreshed successfully
[[19:01:11]] [SUCCESS] Screenshot refreshed successfully
[[19:01:11]] [INFO] F4NGh9HrLw=running
[[19:01:11]] [INFO] Executing action 446/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:01:10]] [SUCCESS] Screenshot refreshed
[[19:01:10]] [INFO] Refreshing screenshot...
[[19:01:10]] [INFO] 0f2FSZYjWq=pass
[[19:00:53]] [SUCCESS] Screenshot refreshed successfully
[[19:00:53]] [SUCCESS] Screenshot refreshed successfully
[[19:00:52]] [INFO] 0f2FSZYjWq=running
[[19:00:52]] [INFO] Executing action 445/576: Check if element with text="Melbourne" exists
[[19:00:52]] [SUCCESS] Screenshot refreshed
[[19:00:52]] [INFO] Refreshing screenshot...
[[19:00:52]] [INFO] Tebej51pT2=pass
[[19:00:48]] [SUCCESS] Screenshot refreshed successfully
[[19:00:48]] [SUCCESS] Screenshot refreshed successfully
[[19:00:48]] [INFO] Tebej51pT2=running
[[19:00:48]] [INFO] Executing action 444/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[19:00:47]] [SUCCESS] Screenshot refreshed
[[19:00:47]] [INFO] Refreshing screenshot...
[[19:00:47]] [INFO] I4gwigwXSj=pass
[[19:00:44]] [SUCCESS] Screenshot refreshed successfully
[[19:00:44]] [SUCCESS] Screenshot refreshed successfully
[[19:00:44]] [INFO] I4gwigwXSj=running
[[19:00:44]] [INFO] Executing action 443/576: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[19:00:43]] [SUCCESS] Screenshot refreshed
[[19:00:43]] [INFO] Refreshing screenshot...
[[19:00:43]] [INFO] eVytJrry9x=pass
[[19:00:37]] [SUCCESS] Screenshot refreshed successfully
[[19:00:37]] [SUCCESS] Screenshot refreshed successfully
[[19:00:37]] [INFO] eVytJrry9x=running
[[19:00:37]] [INFO] Executing action 442/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[19:00:36]] [SUCCESS] Screenshot refreshed
[[19:00:36]] [INFO] Refreshing screenshot...
[[19:00:36]] [INFO] s8h8VDUIOC=pass
[[19:00:33]] [SUCCESS] Screenshot refreshed successfully
[[19:00:33]] [SUCCESS] Screenshot refreshed successfully
[[19:00:33]] [INFO] s8h8VDUIOC=running
[[19:00:33]] [INFO] Executing action 441/576: Swipe from (50%, 70%) to (50%, 30%)
[[19:00:32]] [SUCCESS] Screenshot refreshed
[[19:00:32]] [INFO] Refreshing screenshot...
[[19:00:32]] [INFO] bkU728TrRF=pass
[[19:00:26]] [SUCCESS] Screenshot refreshed successfully
[[19:00:26]] [SUCCESS] Screenshot refreshed successfully
[[19:00:26]] [INFO] bkU728TrRF=running
[[19:00:26]] [INFO] Executing action 440/576: Tap on element with accessibility_id: Done
[[19:00:26]] [SUCCESS] Screenshot refreshed
[[19:00:26]] [INFO] Refreshing screenshot...
[[19:00:26]] [INFO] ZWpYNcpbFA=pass
[[19:00:21]] [SUCCESS] Screenshot refreshed successfully
[[19:00:21]] [SUCCESS] Screenshot refreshed successfully
[[19:00:21]] [INFO] ZWpYNcpbFA=running
[[19:00:21]] [INFO] Executing action 439/576: Tap on Text: "VIC"
[[19:00:20]] [SUCCESS] Screenshot refreshed
[[19:00:20]] [INFO] Refreshing screenshot...
[[19:00:20]] [INFO] Wld5Urg70o=pass
[[19:00:13]] [SUCCESS] Screenshot refreshed successfully
[[19:00:13]] [SUCCESS] Screenshot refreshed successfully
[[19:00:13]] [INFO] Wld5Urg70o=running
[[19:00:13]] [INFO] Executing action 438/576: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[19:00:13]] [SUCCESS] Screenshot refreshed
[[19:00:13]] [INFO] Refreshing screenshot...
[[19:00:13]] [INFO] QpBLC6BStn=pass
[[19:00:06]] [SUCCESS] Screenshot refreshed successfully
[[19:00:06]] [SUCCESS] Screenshot refreshed successfully
[[19:00:06]] [INFO] QpBLC6BStn=running
[[19:00:06]] [INFO] Executing action 437/576: Tap on element with accessibility_id: delete
[[19:00:05]] [SUCCESS] Screenshot refreshed
[[19:00:05]] [INFO] Refreshing screenshot...
[[19:00:05]] [INFO] G4A3KBlXHq=pass
[[19:00:00]] [SUCCESS] Screenshot refreshed successfully
[[19:00:00]] [SUCCESS] Screenshot refreshed successfully
[[19:00:00]] [INFO] G4A3KBlXHq=running
[[19:00:00]] [INFO] Executing action 436/576: Tap on Text: "Nearby"
[[18:59:59]] [SUCCESS] Screenshot refreshed
[[18:59:59]] [INFO] Refreshing screenshot...
[[18:59:59]] [INFO] uArzgeZYf7=pass
[[18:59:56]] [SUCCESS] Screenshot refreshed successfully
[[18:59:56]] [SUCCESS] Screenshot refreshed successfully
[[18:59:56]] [INFO] uArzgeZYf7=running
[[18:59:56]] [INFO] Executing action 435/576: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[18:59:55]] [SUCCESS] Screenshot refreshed
[[18:59:55]] [INFO] Refreshing screenshot...
[[18:59:55]] [INFO] 3gJsiap2Ds=pass
[[18:59:51]] [SUCCESS] Screenshot refreshed successfully
[[18:59:51]] [SUCCESS] Screenshot refreshed successfully
[[18:59:51]] [INFO] 3gJsiap2Ds=running
[[18:59:51]] [INFO] Executing action 434/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[18:59:51]] [SUCCESS] Screenshot refreshed
[[18:59:51]] [INFO] Refreshing screenshot...
[[18:59:51]] [INFO] dF3hpprg71=pass
[[18:59:47]] [INFO] dF3hpprg71=running
[[18:59:47]] [INFO] Executing action 433/576: Wait till xpath=//XCUIElementTypeOther[@name="Delivery options"]/XCUIElementTypeButton[3]
[[18:59:47]] [SUCCESS] Screenshot refreshed successfully
[[18:59:47]] [SUCCESS] Screenshot refreshed successfully
[[18:59:47]] [SUCCESS] Screenshot refreshed
[[18:59:47]] [INFO] Refreshing screenshot...
[[18:59:47]] [INFO] 94ikwhIEE2=pass
[[18:59:43]] [SUCCESS] Screenshot refreshed successfully
[[18:59:43]] [SUCCESS] Screenshot refreshed successfully
[[18:59:43]] [INFO] 94ikwhIEE2=running
[[18:59:43]] [INFO] Executing action 432/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:59:42]] [SUCCESS] Screenshot refreshed
[[18:59:42]] [INFO] Refreshing screenshot...
[[18:59:42]] [INFO] q8oldD8uZt=pass
[[18:59:39]] [SUCCESS] Screenshot refreshed successfully
[[18:59:39]] [SUCCESS] Screenshot refreshed successfully
[[18:59:38]] [INFO] q8oldD8uZt=running
[[18:59:38]] [INFO] Executing action 431/576: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[18:59:38]] [SUCCESS] Screenshot refreshed
[[18:59:38]] [INFO] Refreshing screenshot...
[[18:59:38]] [INFO] Jf2wJyOphY=pass
[[18:59:30]] [SUCCESS] Screenshot refreshed successfully
[[18:59:30]] [SUCCESS] Screenshot refreshed successfully
[[18:59:30]] [INFO] Jf2wJyOphY=running
[[18:59:30]] [INFO] Executing action 430/576: Tap on element with accessibility_id: Add to bag
[[18:59:29]] [SUCCESS] Screenshot refreshed
[[18:59:29]] [INFO] Refreshing screenshot...
[[18:59:29]] [INFO] eRCmRhc3re=pass
[[18:59:15]] [INFO] eRCmRhc3re=running
[[18:59:15]] [INFO] Executing action 429/576: Check if element with text="Broadway" exists
[[18:59:15]] [SUCCESS] Screenshot refreshed successfully
[[18:59:15]] [SUCCESS] Screenshot refreshed successfully
[[18:59:15]] [SUCCESS] Screenshot refreshed
[[18:59:15]] [INFO] Refreshing screenshot...
[[18:59:15]] [INFO] ORI6ZFMBK1=pass
[[18:59:10]] [SUCCESS] Screenshot refreshed successfully
[[18:59:10]] [SUCCESS] Screenshot refreshed successfully
[[18:59:10]] [INFO] ORI6ZFMBK1=running
[[18:59:10]] [INFO] Executing action 428/576: Tap on Text: "Save"
[[18:59:09]] [SUCCESS] Screenshot refreshed
[[18:59:09]] [INFO] Refreshing screenshot...
[[18:59:09]] [INFO] hr0IVckpYI=pass
[[18:59:04]] [SUCCESS] Screenshot refreshed successfully
[[18:59:04]] [SUCCESS] Screenshot refreshed successfully
[[18:59:04]] [INFO] hr0IVckpYI=running
[[18:59:04]] [INFO] Executing action 427/576: Wait till accessibility_id=btnSaveOrContinue
[[18:59:03]] [SUCCESS] Screenshot refreshed
[[18:59:03]] [INFO] Refreshing screenshot...
[[18:59:03]] [INFO] H0ODFz7sWJ=pass
[[18:58:59]] [SUCCESS] Screenshot refreshed successfully
[[18:58:59]] [SUCCESS] Screenshot refreshed successfully
[[18:58:59]] [INFO] H0ODFz7sWJ=running
[[18:58:59]] [INFO] Executing action 426/576: Tap on Text: "2000"
[[18:58:58]] [SUCCESS] Screenshot refreshed
[[18:58:58]] [INFO] Refreshing screenshot...
[[18:58:58]] [INFO] uZHvvAzVfx=pass
[[18:58:53]] [INFO] uZHvvAzVfx=running
[[18:58:53]] [INFO] Executing action 425/576: textClear action
[[18:58:53]] [SUCCESS] Screenshot refreshed successfully
[[18:58:53]] [SUCCESS] Screenshot refreshed successfully
[[18:58:53]] [SUCCESS] Screenshot refreshed
[[18:58:53]] [INFO] Refreshing screenshot...
[[18:58:53]] [INFO] WmNWcsWVHv=pass
[[18:58:47]] [SUCCESS] Screenshot refreshed successfully
[[18:58:47]] [SUCCESS] Screenshot refreshed successfully
[[18:58:47]] [INFO] WmNWcsWVHv=running
[[18:58:47]] [INFO] Executing action 424/576: Tap on element with accessibility_id: Search suburb or postcode
[[18:58:47]] [SUCCESS] Screenshot refreshed
[[18:58:47]] [INFO] Refreshing screenshot...
[[18:58:47]] [INFO] lnjoz8hHUU=pass
[[18:58:42]] [SUCCESS] Screenshot refreshed successfully
[[18:58:42]] [SUCCESS] Screenshot refreshed successfully
[[18:58:41]] [INFO] lnjoz8hHUU=running
[[18:58:41]] [INFO] Executing action 423/576: Tap on Text: "Edit"
[[18:58:41]] [SUCCESS] Screenshot refreshed
[[18:58:41]] [INFO] Refreshing screenshot...
[[18:58:41]] [INFO] letbbewlnA=pass
[[18:58:36]] [SUCCESS] Screenshot refreshed successfully
[[18:58:36]] [SUCCESS] Screenshot refreshed successfully
[[18:58:35]] [INFO] letbbewlnA=running
[[18:58:35]] [INFO] Executing action 422/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:58:35]] [SUCCESS] Screenshot refreshed
[[18:58:35]] [INFO] Refreshing screenshot...
[[18:58:35]] [INFO] trBISwJ8eZ=pass
[[18:58:31]] [SUCCESS] Screenshot refreshed successfully
[[18:58:31]] [SUCCESS] Screenshot refreshed successfully
[[18:58:31]] [INFO] trBISwJ8eZ=running
[[18:58:31]] [INFO] Executing action 421/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:58:30]] [SUCCESS] Screenshot refreshed
[[18:58:30]] [INFO] Refreshing screenshot...
[[18:58:30]] [INFO] foVGMl9wvu=pass
[[18:58:27]] [SUCCESS] Screenshot refreshed successfully
[[18:58:27]] [SUCCESS] Screenshot refreshed successfully
[[18:58:26]] [INFO] foVGMl9wvu=running
[[18:58:26]] [INFO] Executing action 420/576: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:58:26]] [SUCCESS] Screenshot refreshed
[[18:58:26]] [INFO] Refreshing screenshot...
[[18:58:26]] [INFO] 73NABkfWyY=pass
[[18:58:11]] [SUCCESS] Screenshot refreshed successfully
[[18:58:11]] [SUCCESS] Screenshot refreshed successfully
[[18:58:10]] [INFO] 73NABkfWyY=running
[[18:58:10]] [INFO] Executing action 419/576: Check if element with text="Sanctuary" exists
[[18:58:10]] [SUCCESS] Screenshot refreshed
[[18:58:10]] [INFO] Refreshing screenshot...
[[18:58:10]] [INFO] pKjXoj4mNg=pass
[[18:58:05]] [SUCCESS] Screenshot refreshed successfully
[[18:58:05]] [SUCCESS] Screenshot refreshed successfully
[[18:58:05]] [INFO] pKjXoj4mNg=running
[[18:58:05]] [INFO] Executing action 418/576: Tap on Text: "Save"
[[18:58:04]] [SUCCESS] Screenshot refreshed
[[18:58:04]] [INFO] Refreshing screenshot...
[[18:58:04]] [INFO] M3dXqigqRv=pass
[[18:57:59]] [SUCCESS] Screenshot refreshed successfully
[[18:57:59]] [SUCCESS] Screenshot refreshed successfully
[[18:57:59]] [INFO] M3dXqigqRv=running
[[18:57:59]] [INFO] Executing action 417/576: Wait till accessibility_id=btnSaveOrContinue
[[18:57:59]] [SUCCESS] Screenshot refreshed
[[18:57:59]] [INFO] Refreshing screenshot...
[[18:57:59]] [INFO] GYRHQr7TWx=pass
[[18:57:54]] [SUCCESS] Screenshot refreshed successfully
[[18:57:54]] [SUCCESS] Screenshot refreshed successfully
[[18:57:54]] [INFO] GYRHQr7TWx=running
[[18:57:54]] [INFO] Executing action 416/576: Tap on Text: "current"
[[18:57:53]] [SUCCESS] Screenshot refreshed
[[18:57:53]] [INFO] Refreshing screenshot...
[[18:57:53]] [INFO] kiM0WyWE9I=pass
[[18:57:49]] [SUCCESS] Screenshot refreshed successfully
[[18:57:49]] [SUCCESS] Screenshot refreshed successfully
[[18:57:49]] [INFO] kiM0WyWE9I=running
[[18:57:49]] [INFO] Executing action 415/576: Wait till accessibility_id=btnCurrentLocationButton
[[18:57:48]] [SUCCESS] Screenshot refreshed
[[18:57:48]] [INFO] Refreshing screenshot...
[[18:57:48]] [INFO] VkUKQbf1Qt=pass
[[18:57:43]] [SUCCESS] Screenshot refreshed successfully
[[18:57:43]] [SUCCESS] Screenshot refreshed successfully
[[18:57:43]] [INFO] VkUKQbf1Qt=running
[[18:57:43]] [INFO] Executing action 414/576: Tap on Text: "Edit"
[[18:57:42]] [SUCCESS] Screenshot refreshed
[[18:57:42]] [INFO] Refreshing screenshot...
[[18:57:42]] [INFO] C6JHhLdWTv=pass
[[18:57:39]] [SUCCESS] Screenshot refreshed successfully
[[18:57:39]] [SUCCESS] Screenshot refreshed successfully
[[18:57:38]] [INFO] C6JHhLdWTv=running
[[18:57:38]] [INFO] Executing action 413/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:57:38]] [SUCCESS] Screenshot refreshed
[[18:57:38]] [INFO] Refreshing screenshot...
[[18:57:38]] [INFO] IupxLP2Jsr=pass
[[18:57:34]] [SUCCESS] Screenshot refreshed successfully
[[18:57:34]] [SUCCESS] Screenshot refreshed successfully
[[18:57:34]] [INFO] IupxLP2Jsr=running
[[18:57:34]] [INFO] Executing action 412/576: iOS Function: text - Text: "Uno card"
[[18:57:33]] [SUCCESS] Screenshot refreshed
[[18:57:33]] [INFO] Refreshing screenshot...
[[18:57:33]] [INFO] 70iOOakiG7=pass
[[18:57:28]] [SUCCESS] Screenshot refreshed successfully
[[18:57:28]] [SUCCESS] Screenshot refreshed successfully
[[18:57:28]] [INFO] 70iOOakiG7=running
[[18:57:28]] [INFO] Executing action 411/576: Tap on Text: "Find"
[[18:57:27]] [SUCCESS] Screenshot refreshed
[[18:57:27]] [INFO] Refreshing screenshot...
[[18:57:27]] [INFO] Xqj9EIVEfg=pass
[[18:57:19]] [SUCCESS] Screenshot refreshed successfully
[[18:57:19]] [SUCCESS] Screenshot refreshed successfully
[[18:57:19]] [INFO] Xqj9EIVEfg=running
[[18:57:19]] [INFO] Executing action 410/576: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[18:57:19]] [SUCCESS] Screenshot refreshed
[[18:57:19]] [INFO] Refreshing screenshot...
[[18:57:19]] [INFO] E2jpN7BioW=pass
[[18:57:14]] [SUCCESS] Screenshot refreshed successfully
[[18:57:14]] [SUCCESS] Screenshot refreshed successfully
[[18:57:14]] [INFO] E2jpN7BioW=running
[[18:57:14]] [INFO] Executing action 409/576: Tap on Text: "Save"
[[18:57:13]] [SUCCESS] Screenshot refreshed
[[18:57:13]] [INFO] Refreshing screenshot...
[[18:57:13]] [INFO] Sl6eiqZkRm=pass
[[18:57:09]] [SUCCESS] Screenshot refreshed successfully
[[18:57:09]] [SUCCESS] Screenshot refreshed successfully
[[18:57:09]] [INFO] Sl6eiqZkRm=running
[[18:57:09]] [INFO] Executing action 408/576: Wait till accessibility_id=btnSaveOrContinue
[[18:57:08]] [SUCCESS] Screenshot refreshed
[[18:57:08]] [INFO] Refreshing screenshot...
[[18:57:08]] [INFO] mw9GQ4mzRE=pass
[[18:57:03]] [SUCCESS] Screenshot refreshed successfully
[[18:57:03]] [SUCCESS] Screenshot refreshed successfully
[[18:57:03]] [INFO] mw9GQ4mzRE=running
[[18:57:03]] [INFO] Executing action 407/576: Tap on Text: "2000"
[[18:57:03]] [SUCCESS] Screenshot refreshed
[[18:57:03]] [INFO] Refreshing screenshot...
[[18:57:03]] [INFO] kbdEPCPYod=pass
[[18:56:58]] [INFO] kbdEPCPYod=running
[[18:56:58]] [INFO] Executing action 406/576: textClear action
[[18:56:58]] [SUCCESS] Screenshot refreshed successfully
[[18:56:58]] [SUCCESS] Screenshot refreshed successfully
[[18:56:57]] [SUCCESS] Screenshot refreshed
[[18:56:57]] [INFO] Refreshing screenshot...
[[18:56:57]] [INFO] 8WCusTZ8q9=pass
[[18:56:52]] [SUCCESS] Screenshot refreshed successfully
[[18:56:52]] [SUCCESS] Screenshot refreshed successfully
[[18:56:52]] [INFO] 8WCusTZ8q9=running
[[18:56:52]] [INFO] Executing action 405/576: Tap on element with accessibility_id: Search suburb or postcode
[[18:56:51]] [SUCCESS] Screenshot refreshed
[[18:56:51]] [INFO] Refreshing screenshot...
[[18:56:51]] [INFO] QMXBlswP6H=pass
[[18:56:48]] [SUCCESS] Screenshot refreshed successfully
[[18:56:48]] [SUCCESS] Screenshot refreshed successfully
[[18:56:47]] [INFO] QMXBlswP6H=running
[[18:56:47]] [INFO] Executing action 404/576: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[18:56:47]] [SUCCESS] Screenshot refreshed
[[18:56:47]] [INFO] Refreshing screenshot...
[[18:56:47]] [INFO] m0956RsrdM=pass
[[18:56:45]] [SUCCESS] Screenshot refreshed successfully
[[18:56:45]] [SUCCESS] Screenshot refreshed successfully
[[18:56:43]] [INFO] m0956RsrdM=running
[[18:56:43]] [INFO] Executing action 403/576: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[18:56:43]] [SUCCESS] Screenshot refreshed
[[18:56:43]] [INFO] Refreshing screenshot...
[[18:56:42]] [SUCCESS] Screenshot refreshed
[[18:56:42]] [INFO] Refreshing screenshot...
[[18:56:38]] [SUCCESS] Screenshot refreshed successfully
[[18:56:38]] [SUCCESS] Screenshot refreshed successfully
[[18:56:38]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:56:37]] [SUCCESS] Screenshot refreshed
[[18:56:37]] [INFO] Refreshing screenshot...
[[18:56:33]] [SUCCESS] Screenshot refreshed successfully
[[18:56:33]] [SUCCESS] Screenshot refreshed successfully
[[18:56:33]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:56:32]] [SUCCESS] Screenshot refreshed
[[18:56:32]] [INFO] Refreshing screenshot...
[[18:56:27]] [SUCCESS] Screenshot refreshed successfully
[[18:56:27]] [SUCCESS] Screenshot refreshed successfully
[[18:56:27]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[18:56:27]] [SUCCESS] Screenshot refreshed
[[18:56:27]] [INFO] Refreshing screenshot...
[[18:56:23]] [SUCCESS] Screenshot refreshed successfully
[[18:56:23]] [SUCCESS] Screenshot refreshed successfully
[[18:56:23]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:56:22]] [SUCCESS] Screenshot refreshed
[[18:56:22]] [INFO] Refreshing screenshot...
[[18:56:17]] [SUCCESS] Screenshot refreshed successfully
[[18:56:17]] [SUCCESS] Screenshot refreshed successfully
[[18:56:16]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:56:16]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:56:16]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[18:56:16]] [INFO] C3UHsKxa5P=running
[[18:56:16]] [INFO] Executing action 402/576: Execute Test Case: Kmart-Signin (6 steps)
[[18:56:16]] [SUCCESS] Screenshot refreshed
[[18:56:16]] [INFO] Refreshing screenshot...
[[18:56:16]] [INFO] Azb1flbIJJ=pass
[[18:56:12]] [SUCCESS] Screenshot refreshed successfully
[[18:56:12]] [SUCCESS] Screenshot refreshed successfully
[[18:56:12]] [INFO] Azb1flbIJJ=running
[[18:56:12]] [INFO] Executing action 401/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:56:12]] [SUCCESS] Screenshot refreshed
[[18:56:12]] [INFO] Refreshing screenshot...
[[18:56:12]] [INFO] 2xC5fLfLe8=pass
[[18:56:09]] [SUCCESS] Screenshot refreshed successfully
[[18:56:09]] [SUCCESS] Screenshot refreshed successfully
[[18:56:09]] [INFO] 2xC5fLfLe8=running
[[18:56:09]] [INFO] Executing action 400/576: iOS Function: alert_accept
[[18:56:08]] [SUCCESS] Screenshot refreshed
[[18:56:08]] [INFO] Refreshing screenshot...
[[18:56:08]] [INFO] Y8vz7AJD1i=pass
[[18:56:01]] [SUCCESS] Screenshot refreshed successfully
[[18:56:01]] [SUCCESS] Screenshot refreshed successfully
[[18:56:01]] [INFO] Y8vz7AJD1i=running
[[18:56:01]] [INFO] Executing action 399/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:56:00]] [SUCCESS] Screenshot refreshed
[[18:56:00]] [INFO] Refreshing screenshot...
[[18:56:00]] [INFO] H9fy9qcFbZ=pass
[[18:55:47]] [SUCCESS] Screenshot refreshed successfully
[[18:55:47]] [SUCCESS] Screenshot refreshed successfully
[[18:55:46]] [INFO] H9fy9qcFbZ=running
[[18:55:46]] [INFO] Executing action 398/576: Restart app: env[appid]
[[18:55:46]] [SUCCESS] Screenshot refreshed
[[18:55:46]] [INFO] Refreshing screenshot...
[[18:55:46]] [INFO] OMgc2gHHyq=pass
[[18:55:21]] [SUCCESS] Screenshot refreshed successfully
[[18:55:21]] [SUCCESS] Screenshot refreshed successfully
[[18:55:20]] [INFO] OMgc2gHHyq=running
[[18:55:20]] [INFO] Executing action 397/576: cleanupSteps action
[[18:55:19]] [SUCCESS] Screenshot refreshed
[[18:55:19]] [INFO] Refreshing screenshot...
[[18:55:19]] [INFO] x4yLCZHaCR=pass
[[18:55:17]] [SUCCESS] Screenshot refreshed successfully
[[18:55:17]] [SUCCESS] Screenshot refreshed successfully
[[18:55:16]] [INFO] x4yLCZHaCR=running
[[18:55:16]] [INFO] Executing action 396/576: Terminate app: env[appid]
[[18:55:16]] [SUCCESS] Screenshot refreshed
[[18:55:16]] [INFO] Refreshing screenshot...
[[18:55:16]] [INFO] 2p13JoJbbA=pass
[[18:55:11]] [SUCCESS] Screenshot refreshed successfully
[[18:55:11]] [SUCCESS] Screenshot refreshed successfully
[[18:55:11]] [INFO] 2p13JoJbbA=running
[[18:55:11]] [INFO] Executing action 395/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:55:11]] [SUCCESS] Screenshot refreshed
[[18:55:11]] [INFO] Refreshing screenshot...
[[18:55:11]] [INFO] qHdMgerbTE=pass
[[18:55:06]] [SUCCESS] Screenshot refreshed successfully
[[18:55:06]] [SUCCESS] Screenshot refreshed successfully
[[18:55:06]] [INFO] qHdMgerbTE=running
[[18:55:06]] [INFO] Executing action 394/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:55:05]] [SUCCESS] Screenshot refreshed
[[18:55:05]] [INFO] Refreshing screenshot...
[[18:55:05]] [INFO] F4NGh9HrLw=pass
[[18:55:03]] [SUCCESS] Screenshot refreshed successfully
[[18:55:03]] [SUCCESS] Screenshot refreshed successfully
[[18:55:01]] [INFO] F4NGh9HrLw=running
[[18:55:01]] [INFO] Executing action 393/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:55:01]] [SUCCESS] Screenshot refreshed
[[18:55:01]] [INFO] Refreshing screenshot...
[[18:55:00]] [SUCCESS] Screenshot refreshed
[[18:55:00]] [INFO] Refreshing screenshot...
[[18:54:56]] [SUCCESS] Screenshot refreshed successfully
[[18:54:56]] [SUCCESS] Screenshot refreshed successfully
[[18:54:56]] [INFO] Executing Multi Step action step 41/41: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[18:54:56]] [SUCCESS] Screenshot refreshed
[[18:54:56]] [INFO] Refreshing screenshot...
[[18:54:52]] [SUCCESS] Screenshot refreshed successfully
[[18:54:52]] [SUCCESS] Screenshot refreshed successfully
[[18:54:52]] [INFO] Executing Multi Step action step 40/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:54:51]] [SUCCESS] Screenshot refreshed
[[18:54:51]] [INFO] Refreshing screenshot...
[[18:54:45]] [SUCCESS] Screenshot refreshed successfully
[[18:54:45]] [SUCCESS] Screenshot refreshed successfully
[[18:54:45]] [INFO] Executing Multi Step action step 39/41: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[18:54:44]] [SUCCESS] Screenshot refreshed
[[18:54:44]] [INFO] Refreshing screenshot...
[[18:54:40]] [INFO] Executing Multi Step action step 38/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:54:40]] [SUCCESS] Screenshot refreshed successfully
[[18:54:40]] [SUCCESS] Screenshot refreshed successfully
[[18:54:40]] [SUCCESS] Screenshot refreshed
[[18:54:40]] [INFO] Refreshing screenshot...
[[18:54:35]] [SUCCESS] Screenshot refreshed successfully
[[18:54:35]] [SUCCESS] Screenshot refreshed successfully
[[18:54:35]] [INFO] Executing Multi Step action step 37/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:54:34]] [SUCCESS] Screenshot refreshed
[[18:54:34]] [INFO] Refreshing screenshot...
[[18:54:30]] [SUCCESS] Screenshot refreshed successfully
[[18:54:30]] [SUCCESS] Screenshot refreshed successfully
[[18:54:30]] [INFO] Executing Multi Step action step 36/41: Tap on image: banner-close-updated.png
[[18:54:29]] [SUCCESS] Screenshot refreshed
[[18:54:29]] [INFO] Refreshing screenshot...
[[18:54:26]] [INFO] Executing Multi Step action step 35/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[18:54:26]] [SUCCESS] Screenshot refreshed successfully
[[18:54:26]] [SUCCESS] Screenshot refreshed successfully
[[18:54:26]] [SUCCESS] Screenshot refreshed
[[18:54:26]] [INFO] Refreshing screenshot...
[[18:54:22]] [SUCCESS] Screenshot refreshed successfully
[[18:54:22]] [SUCCESS] Screenshot refreshed successfully
[[18:54:22]] [INFO] Executing Multi Step action step 34/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[18:54:21]] [SUCCESS] Screenshot refreshed
[[18:54:21]] [INFO] Refreshing screenshot...
[[18:54:17]] [SUCCESS] Screenshot refreshed successfully
[[18:54:17]] [SUCCESS] Screenshot refreshed successfully
[[18:54:17]] [INFO] Executing Multi Step action step 33/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[18:54:17]] [SUCCESS] Screenshot refreshed
[[18:54:17]] [INFO] Refreshing screenshot...
[[18:54:12]] [SUCCESS] Screenshot refreshed successfully
[[18:54:12]] [SUCCESS] Screenshot refreshed successfully
[[18:54:12]] [INFO] Executing Multi Step action step 32/41: Tap on image: banner-close-updated.png
[[18:54:12]] [SUCCESS] Screenshot refreshed
[[18:54:12]] [INFO] Refreshing screenshot...
[[18:54:08]] [INFO] Executing Multi Step action step 31/41: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[18:54:08]] [SUCCESS] Screenshot refreshed successfully
[[18:54:08]] [SUCCESS] Screenshot refreshed successfully
[[18:54:07]] [SUCCESS] Screenshot refreshed
[[18:54:07]] [INFO] Refreshing screenshot...
[[18:54:03]] [SUCCESS] Screenshot refreshed successfully
[[18:54:03]] [SUCCESS] Screenshot refreshed successfully
[[18:54:03]] [INFO] Executing Multi Step action step 30/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[18:54:02]] [SUCCESS] Screenshot refreshed
[[18:54:02]] [INFO] Refreshing screenshot...
[[18:53:58]] [SUCCESS] Screenshot refreshed successfully
[[18:53:58]] [SUCCESS] Screenshot refreshed successfully
[[18:53:58]] [INFO] Executing Multi Step action step 29/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[18:53:58]] [SUCCESS] Screenshot refreshed
[[18:53:58]] [INFO] Refreshing screenshot...
[[18:53:54]] [SUCCESS] Screenshot refreshed successfully
[[18:53:54]] [SUCCESS] Screenshot refreshed successfully
[[18:53:53]] [INFO] Executing Multi Step action step 28/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[18:53:53]] [SUCCESS] Screenshot refreshed
[[18:53:53]] [INFO] Refreshing screenshot...
[[18:53:50]] [SUCCESS] Screenshot refreshed successfully
[[18:53:50]] [SUCCESS] Screenshot refreshed successfully
[[18:53:50]] [INFO] Executing Multi Step action step 27/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[18:53:49]] [SUCCESS] Screenshot refreshed
[[18:53:49]] [INFO] Refreshing screenshot...
[[18:53:45]] [SUCCESS] Screenshot refreshed successfully
[[18:53:45]] [SUCCESS] Screenshot refreshed successfully
[[18:53:45]] [INFO] Executing Multi Step action step 26/41: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[18:53:45]] [SUCCESS] Screenshot refreshed
[[18:53:45]] [INFO] Refreshing screenshot...
[[18:53:41]] [SUCCESS] Screenshot refreshed successfully
[[18:53:41]] [SUCCESS] Screenshot refreshed successfully
[[18:53:41]] [INFO] Executing Multi Step action step 25/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[18:53:40]] [SUCCESS] Screenshot refreshed
[[18:53:40]] [INFO] Refreshing screenshot...
[[18:53:35]] [SUCCESS] Screenshot refreshed successfully
[[18:53:35]] [SUCCESS] Screenshot refreshed successfully
[[18:53:35]] [INFO] Executing Multi Step action step 24/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[18:53:35]] [SUCCESS] Screenshot refreshed
[[18:53:35]] [INFO] Refreshing screenshot...
[[18:53:31]] [SUCCESS] Screenshot refreshed successfully
[[18:53:31]] [SUCCESS] Screenshot refreshed successfully
[[18:53:31]] [INFO] Executing Multi Step action step 23/41: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[18:53:31]] [SUCCESS] Screenshot refreshed
[[18:53:31]] [INFO] Refreshing screenshot...
[[18:53:27]] [SUCCESS] Screenshot refreshed successfully
[[18:53:27]] [SUCCESS] Screenshot refreshed successfully
[[18:53:27]] [INFO] Executing Multi Step action step 22/41: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[18:53:26]] [SUCCESS] Screenshot refreshed
[[18:53:26]] [INFO] Refreshing screenshot...
[[18:53:18]] [SUCCESS] Screenshot refreshed successfully
[[18:53:18]] [SUCCESS] Screenshot refreshed successfully
[[18:53:18]] [INFO] Executing Multi Step action step 21/41: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[18:53:18]] [SUCCESS] Screenshot refreshed
[[18:53:18]] [INFO] Refreshing screenshot...
[[18:53:14]] [SUCCESS] Screenshot refreshed successfully
[[18:53:14]] [SUCCESS] Screenshot refreshed successfully
[[18:53:14]] [INFO] Executing Multi Step action step 20/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[18:53:13]] [SUCCESS] Screenshot refreshed
[[18:53:13]] [INFO] Refreshing screenshot...
[[18:53:09]] [SUCCESS] Screenshot refreshed successfully
[[18:53:09]] [SUCCESS] Screenshot refreshed successfully
[[18:53:09]] [INFO] Executing Multi Step action step 19/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[18:53:09]] [SUCCESS] Screenshot refreshed
[[18:53:09]] [INFO] Refreshing screenshot...
[[18:53:00]] [SUCCESS] Screenshot refreshed successfully
[[18:53:00]] [SUCCESS] Screenshot refreshed successfully
[[18:53:00]] [INFO] Executing Multi Step action step 18/41: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[18:53:00]] [SUCCESS] Screenshot refreshed
[[18:53:00]] [INFO] Refreshing screenshot...
[[18:52:56]] [SUCCESS] Screenshot refreshed successfully
[[18:52:56]] [SUCCESS] Screenshot refreshed successfully
[[18:52:56]] [INFO] Executing Multi Step action step 17/41: Tap on image: env[delivery-address-img]
[[18:52:55]] [SUCCESS] Screenshot refreshed
[[18:52:55]] [INFO] Refreshing screenshot...
[[18:52:51]] [SUCCESS] Screenshot refreshed successfully
[[18:52:51]] [SUCCESS] Screenshot refreshed successfully
[[18:52:51]] [INFO] Executing Multi Step action step 16/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:52:50]] [SUCCESS] Screenshot refreshed
[[18:52:50]] [INFO] Refreshing screenshot...
[[18:52:43]] [SUCCESS] Screenshot refreshed successfully
[[18:52:43]] [SUCCESS] Screenshot refreshed successfully
[[18:52:43]] [INFO] Executing Multi Step action step 15/41: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "env[deliver-address]"
[[18:52:43]] [SUCCESS] Screenshot refreshed
[[18:52:43]] [INFO] Refreshing screenshot...
[[18:52:38]] [SUCCESS] Screenshot refreshed successfully
[[18:52:38]] [SUCCESS] Screenshot refreshed successfully
[[18:52:38]] [INFO] Executing Multi Step action step 14/41: Tap on Text: "address"
[[18:52:37]] [SUCCESS] Screenshot refreshed
[[18:52:37]] [INFO] Refreshing screenshot...
[[18:52:32]] [SUCCESS] Screenshot refreshed successfully
[[18:52:32]] [SUCCESS] Screenshot refreshed successfully
[[18:52:32]] [INFO] Executing Multi Step action step 13/41: iOS Function: text - Text: " "
[[18:52:32]] [SUCCESS] Screenshot refreshed
[[18:52:32]] [INFO] Refreshing screenshot...
[[18:52:25]] [SUCCESS] Screenshot refreshed successfully
[[18:52:25]] [SUCCESS] Screenshot refreshed successfully
[[18:52:25]] [INFO] Executing Multi Step action step 12/41: textClear action
[[18:52:25]] [SUCCESS] Screenshot refreshed
[[18:52:25]] [INFO] Refreshing screenshot...
[[18:52:21]] [SUCCESS] Screenshot refreshed successfully
[[18:52:21]] [SUCCESS] Screenshot refreshed successfully
[[18:52:21]] [INFO] Executing Multi Step action step 11/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[18:52:20]] [SUCCESS] Screenshot refreshed
[[18:52:20]] [INFO] Refreshing screenshot...
[[18:52:13]] [SUCCESS] Screenshot refreshed successfully
[[18:52:13]] [SUCCESS] Screenshot refreshed successfully
[[18:52:13]] [INFO] Executing Multi Step action step 10/41: textClear action
[[18:52:12]] [SUCCESS] Screenshot refreshed
[[18:52:12]] [INFO] Refreshing screenshot...
[[18:52:08]] [SUCCESS] Screenshot refreshed successfully
[[18:52:08]] [SUCCESS] Screenshot refreshed successfully
[[18:52:08]] [INFO] Executing Multi Step action step 9/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:52:08]] [SUCCESS] Screenshot refreshed
[[18:52:08]] [INFO] Refreshing screenshot...
[[18:52:01]] [SUCCESS] Screenshot refreshed successfully
[[18:52:01]] [SUCCESS] Screenshot refreshed successfully
[[18:52:01]] [INFO] Executing Multi Step action step 8/41: textClear action
[[18:52:01]] [SUCCESS] Screenshot refreshed
[[18:52:01]] [INFO] Refreshing screenshot...
[[18:51:57]] [SUCCESS] Screenshot refreshed successfully
[[18:51:57]] [SUCCESS] Screenshot refreshed successfully
[[18:51:57]] [INFO] Executing Multi Step action step 7/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[18:51:56]] [SUCCESS] Screenshot refreshed
[[18:51:56]] [INFO] Refreshing screenshot...
[[18:51:50]] [SUCCESS] Screenshot refreshed successfully
[[18:51:50]] [SUCCESS] Screenshot refreshed successfully
[[18:51:50]] [INFO] Executing Multi Step action step 6/41: textClear action
[[18:51:49]] [SUCCESS] Screenshot refreshed
[[18:51:49]] [INFO] Refreshing screenshot...
[[18:51:45]] [SUCCESS] Screenshot refreshed successfully
[[18:51:45]] [SUCCESS] Screenshot refreshed successfully
[[18:51:45]] [INFO] Executing Multi Step action step 5/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[18:51:45]] [SUCCESS] Screenshot refreshed
[[18:51:45]] [INFO] Refreshing screenshot...
[[18:51:41]] [SUCCESS] Screenshot refreshed successfully
[[18:51:41]] [SUCCESS] Screenshot refreshed successfully
[[18:51:41]] [INFO] Executing Multi Step action step 4/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[18:51:40]] [SUCCESS] Screenshot refreshed
[[18:51:40]] [INFO] Refreshing screenshot...
[[18:51:21]] [SUCCESS] Screenshot refreshed successfully
[[18:51:21]] [SUCCESS] Screenshot refreshed successfully
[[18:51:21]] [INFO] Executing Multi Step action step 3/41: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[18:51:20]] [SUCCESS] Screenshot refreshed
[[18:51:20]] [INFO] Refreshing screenshot...
[[18:51:16]] [SUCCESS] Screenshot refreshed successfully
[[18:51:16]] [SUCCESS] Screenshot refreshed successfully
[[18:51:16]] [INFO] Executing Multi Step action step 2/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:51:16]] [SUCCESS] Screenshot refreshed
[[18:51:16]] [INFO] Refreshing screenshot...
[[18:51:10]] [INFO] Executing Multi Step action step 1/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:51:10]] [INFO] Loaded 41 steps from test case: Delivery Buy Steps
[[18:51:10]] [SUCCESS] Screenshot refreshed successfully
[[18:51:10]] [SUCCESS] Screenshot refreshed successfully
[[18:51:10]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps
[[18:51:10]] [INFO] ZxObWodIp8=running
[[18:51:10]] [INFO] Executing action 392/576: Execute Test Case: Delivery Buy Steps (41 steps)
[[18:51:09]] [SUCCESS] Screenshot refreshed
[[18:51:09]] [INFO] Refreshing screenshot...
[[18:51:09]] [INFO] F4NGh9HrLw=pass
[[18:51:05]] [SUCCESS] Screenshot refreshed successfully
[[18:51:05]] [SUCCESS] Screenshot refreshed successfully
[[18:51:04]] [INFO] F4NGh9HrLw=running
[[18:51:04]] [INFO] Executing action 391/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:51:04]] [SUCCESS] Screenshot refreshed
[[18:51:04]] [INFO] Refreshing screenshot...
[[18:51:04]] [INFO] 4eEEGs1x8i=pass
[[18:50:52]] [SUCCESS] Screenshot refreshed successfully
[[18:50:52]] [SUCCESS] Screenshot refreshed successfully
[[18:50:51]] [INFO] 4eEEGs1x8i=running
[[18:50:51]] [INFO] Executing action 390/576: If exists: xpath="//XCUIElementTypeButton[@name="Save my location"]" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Save my location"]
[[18:50:51]] [SUCCESS] Screenshot refreshed
[[18:50:51]] [INFO] Refreshing screenshot...
[[18:50:51]] [INFO] O8XvoFFGEB=pass
[[18:50:47]] [SUCCESS] Screenshot refreshed successfully
[[18:50:47]] [SUCCESS] Screenshot refreshed successfully
[[18:50:47]] [INFO] O8XvoFFGEB=running
[[18:50:47]] [INFO] Executing action 389/576: Tap on image: env[atg-pdp]
[[18:50:46]] [SUCCESS] Screenshot refreshed
[[18:50:46]] [INFO] Refreshing screenshot...
[[18:50:46]] [INFO] CcFsA41sKp=pass
[[18:50:42]] [SUCCESS] Screenshot refreshed successfully
[[18:50:42]] [SUCCESS] Screenshot refreshed successfully
[[18:50:42]] [INFO] CcFsA41sKp=running
[[18:50:42]] [INFO] Executing action 388/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:50:41]] [SUCCESS] Screenshot refreshed
[[18:50:41]] [INFO] Refreshing screenshot...
[[18:50:41]] [INFO] 8XWyF2kgwW=pass
[[18:50:38]] [SUCCESS] Screenshot refreshed successfully
[[18:50:38]] [SUCCESS] Screenshot refreshed successfully
[[18:50:38]] [INFO] 8XWyF2kgwW=running
[[18:50:38]] [INFO] Executing action 387/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:50:37]] [SUCCESS] Screenshot refreshed
[[18:50:37]] [INFO] Refreshing screenshot...
[[18:50:37]] [INFO] qG4RkNac30=pass
[[18:50:33]] [SUCCESS] Screenshot refreshed successfully
[[18:50:33]] [SUCCESS] Screenshot refreshed successfully
[[18:50:33]] [INFO] qG4RkNac30=running
[[18:50:33]] [INFO] Executing action 386/576: iOS Function: text - Text: "P_42691341"
[[18:50:32]] [SUCCESS] Screenshot refreshed
[[18:50:32]] [INFO] Refreshing screenshot...
[[18:50:32]] [INFO] Jtn2FK4THX=pass
[[18:50:27]] [SUCCESS] Screenshot refreshed successfully
[[18:50:27]] [SUCCESS] Screenshot refreshed successfully
[[18:50:27]] [INFO] Jtn2FK4THX=running
[[18:50:27]] [INFO] Executing action 385/576: Tap on Text: "Find"
[[18:50:26]] [SUCCESS] Screenshot refreshed
[[18:50:26]] [INFO] Refreshing screenshot...
[[18:50:26]] [INFO] tWq2Qzn22D=pass
[[18:50:22]] [SUCCESS] Screenshot refreshed successfully
[[18:50:22]] [SUCCESS] Screenshot refreshed successfully
[[18:50:22]] [INFO] tWq2Qzn22D=running
[[18:50:22]] [INFO] Executing action 384/576: Tap on image: env[device-back-img]
[[18:50:21]] [SUCCESS] Screenshot refreshed
[[18:50:21]] [INFO] Refreshing screenshot...
[[18:50:21]] [INFO] 5hClb2pKKx=pass
[[18:50:00]] [SUCCESS] Screenshot refreshed successfully
[[18:50:00]] [SUCCESS] Screenshot refreshed successfully
[[18:50:00]] [INFO] 5hClb2pKKx=running
[[18:50:00]] [INFO] Executing action 383/576: If exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="btnUpdate"]
[[18:49:59]] [SUCCESS] Screenshot refreshed
[[18:49:59]] [INFO] Refreshing screenshot...
[[18:49:59]] [INFO] jmKjclMUWT=pass
[[18:49:54]] [SUCCESS] Screenshot refreshed successfully
[[18:49:54]] [SUCCESS] Screenshot refreshed successfully
[[18:49:54]] [INFO] jmKjclMUWT=running
[[18:49:54]] [INFO] Executing action 382/576: Tap on Text: "current"
[[18:49:54]] [SUCCESS] Screenshot refreshed
[[18:49:54]] [INFO] Refreshing screenshot...
[[18:49:54]] [INFO] UoH0wdtcLk=pass
[[18:49:49]] [SUCCESS] Screenshot refreshed successfully
[[18:49:49]] [SUCCESS] Screenshot refreshed successfully
[[18:49:49]] [INFO] UoH0wdtcLk=running
[[18:49:49]] [INFO] Executing action 381/576: Tap on Text: "Edit"
[[18:49:48]] [SUCCESS] Screenshot refreshed
[[18:49:48]] [INFO] Refreshing screenshot...
[[18:49:48]] [INFO] U48qCNydwd=pass
[[18:49:43]] [SUCCESS] Screenshot refreshed successfully
[[18:49:43]] [SUCCESS] Screenshot refreshed successfully
[[18:49:43]] [INFO] U48qCNydwd=running
[[18:49:43]] [INFO] Executing action 380/576: Restart app: env[appid]
[[18:49:42]] [SUCCESS] Screenshot refreshed
[[18:49:42]] [INFO] Refreshing screenshot...
[[18:49:42]] [INFO] XjclKOaCTh=pass
[[18:49:38]] [SUCCESS] Screenshot refreshed successfully
[[18:49:38]] [SUCCESS] Screenshot refreshed successfully
[[18:49:38]] [INFO] XjclKOaCTh=running
[[18:49:38]] [INFO] Executing action 379/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:49:37]] [SUCCESS] Screenshot refreshed
[[18:49:37]] [INFO] Refreshing screenshot...
[[18:49:37]] [INFO] q6cKxgMAIn=pass
[[18:49:34]] [SUCCESS] Screenshot refreshed successfully
[[18:49:34]] [SUCCESS] Screenshot refreshed successfully
[[18:49:33]] [INFO] q6cKxgMAIn=running
[[18:49:33]] [INFO] Executing action 378/576: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[18:49:33]] [SUCCESS] Screenshot refreshed
[[18:49:33]] [INFO] Refreshing screenshot...
[[18:49:33]] [INFO] zdh8hKYC1a=pass
[[18:49:29]] [SUCCESS] Screenshot refreshed successfully
[[18:49:29]] [SUCCESS] Screenshot refreshed successfully
[[18:49:29]] [INFO] zdh8hKYC1a=running
[[18:49:29]] [INFO] Executing action 377/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[18:49:28]] [SUCCESS] Screenshot refreshed
[[18:49:28]] [INFO] Refreshing screenshot...
[[18:49:28]] [INFO] P4b2BITpCf=pass
[[18:49:25]] [SUCCESS] Screenshot refreshed successfully
[[18:49:25]] [SUCCESS] Screenshot refreshed successfully
[[18:49:25]] [INFO] P4b2BITpCf=running
[[18:49:25]] [INFO] Executing action 376/576: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[18:49:25]] [SUCCESS] Screenshot refreshed
[[18:49:25]] [INFO] Refreshing screenshot...
[[18:49:25]] [INFO] inrxgdWzXr=pass
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:20]] [INFO] inrxgdWzXr=running
[[18:49:20]] [INFO] Executing action 375/576: Tap on Text: "Store"
[[18:49:19]] [SUCCESS] Screenshot refreshed
[[18:49:19]] [INFO] Refreshing screenshot...
[[18:49:19]] [INFO] inrxgdWzXr=pass
[[18:49:15]] [SUCCESS] Screenshot refreshed successfully
[[18:49:15]] [SUCCESS] Screenshot refreshed successfully
[[18:49:15]] [INFO] inrxgdWzXr=running
[[18:49:15]] [INFO] Executing action 374/576: Tap on Text: "receipts"
[[18:49:14]] [SUCCESS] Screenshot refreshed
[[18:49:14]] [INFO] Refreshing screenshot...
[[18:49:14]] [INFO] GEMv6goQtW=pass
[[18:49:11]] [SUCCESS] Screenshot refreshed successfully
[[18:49:11]] [SUCCESS] Screenshot refreshed successfully
[[18:49:10]] [INFO] GEMv6goQtW=running
[[18:49:10]] [INFO] Executing action 373/576: Tap on image: env[device-back-img]
[[18:49:10]] [SUCCESS] Screenshot refreshed
[[18:49:10]] [INFO] Refreshing screenshot...
[[18:49:10]] [INFO] DhWa2PCBXE=pass
[[18:49:07]] [SUCCESS] Screenshot refreshed successfully
[[18:49:07]] [SUCCESS] Screenshot refreshed successfully
[[18:49:07]] [INFO] DhWa2PCBXE=running
[[18:49:07]] [INFO] Executing action 372/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[18:49:06]] [SUCCESS] Screenshot refreshed
[[18:49:06]] [INFO] Refreshing screenshot...
[[18:49:06]] [INFO] pk2DLZFBmx=pass
[[18:49:02]] [SUCCESS] Screenshot refreshed successfully
[[18:49:02]] [SUCCESS] Screenshot refreshed successfully
[[18:49:02]] [INFO] pk2DLZFBmx=running
[[18:49:02]] [INFO] Executing action 371/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[18:49:02]] [SUCCESS] Screenshot refreshed
[[18:49:02]] [INFO] Refreshing screenshot...
[[18:49:02]] [INFO] ShJSdXvmVL=pass
[[18:48:58]] [SUCCESS] Screenshot refreshed successfully
[[18:48:58]] [SUCCESS] Screenshot refreshed successfully
[[18:48:57]] [INFO] ShJSdXvmVL=running
[[18:48:57]] [INFO] Executing action 370/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[18:48:57]] [SUCCESS] Screenshot refreshed
[[18:48:57]] [INFO] Refreshing screenshot...
[[18:48:57]] [INFO] kWPRvuo7kk=pass
[[18:48:53]] [SUCCESS] Screenshot refreshed successfully
[[18:48:53]] [SUCCESS] Screenshot refreshed successfully
[[18:48:52]] [INFO] kWPRvuo7kk=running
[[18:48:52]] [INFO] Executing action 369/576: iOS Function: text - Text: "env[pwd-op]"
[[18:48:52]] [SUCCESS] Screenshot refreshed
[[18:48:52]] [INFO] Refreshing screenshot...
[[18:48:52]] [INFO] d6vTfR4Y0D=pass
[[18:48:48]] [SUCCESS] Screenshot refreshed successfully
[[18:48:48]] [SUCCESS] Screenshot refreshed successfully
[[18:48:48]] [INFO] d6vTfR4Y0D=running
[[18:48:48]] [INFO] Executing action 368/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:48:47]] [SUCCESS] Screenshot refreshed
[[18:48:47]] [INFO] Refreshing screenshot...
[[18:48:47]] [INFO] pe9W6tZdXT=pass
[[18:48:42]] [SUCCESS] Screenshot refreshed successfully
[[18:48:42]] [SUCCESS] Screenshot refreshed successfully
[[18:48:42]] [INFO] pe9W6tZdXT=running
[[18:48:42]] [INFO] Executing action 367/576: iOS Function: text - Text: "env[uname-op]"
[[18:48:42]] [SUCCESS] Screenshot refreshed
[[18:48:42]] [INFO] Refreshing screenshot...
[[18:48:42]] [INFO] u928vFzSni=pass
[[18:48:38]] [SUCCESS] Screenshot refreshed successfully
[[18:48:38]] [SUCCESS] Screenshot refreshed successfully
[[18:48:38]] [INFO] u928vFzSni=running
[[18:48:38]] [INFO] Executing action 366/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:48:37]] [SUCCESS] Screenshot refreshed
[[18:48:37]] [INFO] Refreshing screenshot...
[[18:48:37]] [INFO] s0WyiD1w0B=pass
[[18:48:34]] [SUCCESS] Screenshot refreshed successfully
[[18:48:34]] [SUCCESS] Screenshot refreshed successfully
[[18:48:34]] [INFO] s0WyiD1w0B=running
[[18:48:34]] [INFO] Executing action 365/576: iOS Function: alert_accept
[[18:48:33]] [SUCCESS] Screenshot refreshed
[[18:48:33]] [INFO] Refreshing screenshot...
[[18:48:33]] [INFO] gekNSY5O2E=pass
[[18:48:29]] [SUCCESS] Screenshot refreshed successfully
[[18:48:29]] [SUCCESS] Screenshot refreshed successfully
[[18:48:29]] [INFO] gekNSY5O2E=running
[[18:48:29]] [INFO] Executing action 364/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[18:48:29]] [SUCCESS] Screenshot refreshed
[[18:48:29]] [INFO] Refreshing screenshot...
[[18:48:29]] [INFO] VJJ3EXXotU=pass
[[18:48:25]] [SUCCESS] Screenshot refreshed successfully
[[18:48:25]] [SUCCESS] Screenshot refreshed successfully
[[18:48:25]] [INFO] VJJ3EXXotU=running
[[18:48:25]] [INFO] Executing action 363/576: Tap on image: env[device-back-img]
[[18:48:24]] [SUCCESS] Screenshot refreshed
[[18:48:24]] [INFO] Refreshing screenshot...
[[18:48:24]] [INFO] 83tV9A4NOn=pass
[[18:48:21]] [SUCCESS] Screenshot refreshed successfully
[[18:48:21]] [SUCCESS] Screenshot refreshed successfully
[[18:48:21]] [INFO] 83tV9A4NOn=running
[[18:48:21]] [INFO] Executing action 362/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[18:48:21]] [SUCCESS] Screenshot refreshed
[[18:48:21]] [INFO] Refreshing screenshot...
[[18:48:21]] [INFO] aNN0yYFLEd=pass
[[18:48:17]] [SUCCESS] Screenshot refreshed successfully
[[18:48:17]] [SUCCESS] Screenshot refreshed successfully
[[18:48:17]] [INFO] aNN0yYFLEd=running
[[18:48:17]] [INFO] Executing action 361/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[18:48:16]] [SUCCESS] Screenshot refreshed
[[18:48:16]] [INFO] Refreshing screenshot...
[[18:48:16]] [INFO] XJv08Gkucs=pass
[[18:48:13]] [SUCCESS] Screenshot refreshed successfully
[[18:48:13]] [SUCCESS] Screenshot refreshed successfully
[[18:48:13]] [INFO] XJv08Gkucs=running
[[18:48:13]] [INFO] Executing action 360/576: Input text: "env[uname-op]"
[[18:48:13]] [SUCCESS] Screenshot refreshed
[[18:48:13]] [INFO] Refreshing screenshot...
[[18:48:13]] [INFO] kAQ1yIIw3h=pass
[[18:48:09]] [SUCCESS] Screenshot refreshed successfully
[[18:48:09]] [SUCCESS] Screenshot refreshed successfully
[[18:48:09]] [INFO] kAQ1yIIw3h=running
[[18:48:09]] [INFO] Executing action 359/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[18:48:08]] [SUCCESS] Screenshot refreshed
[[18:48:08]] [INFO] Refreshing screenshot...
[[18:48:08]] [INFO] 7YbjwQH1Jc=pass
[[18:48:05]] [SUCCESS] Screenshot refreshed successfully
[[18:48:05]] [SUCCESS] Screenshot refreshed successfully
[[18:48:05]] [INFO] 7YbjwQH1Jc=running
[[18:48:05]] [INFO] Executing action 358/576: Input text: "env[searchorder]"
[[18:48:04]] [SUCCESS] Screenshot refreshed
[[18:48:04]] [INFO] Refreshing screenshot...
[[18:48:04]] [INFO] OmKfD9iBjD=pass
[[18:48:01]] [SUCCESS] Screenshot refreshed successfully
[[18:48:01]] [SUCCESS] Screenshot refreshed successfully
[[18:48:01]] [INFO] OmKfD9iBjD=running
[[18:48:01]] [INFO] Executing action 357/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[18:48:00]] [SUCCESS] Screenshot refreshed
[[18:48:00]] [INFO] Refreshing screenshot...
[[18:48:00]] [INFO] eHLWiRoqqS=pass
[[18:47:56]] [SUCCESS] Screenshot refreshed successfully
[[18:47:56]] [SUCCESS] Screenshot refreshed successfully
[[18:47:56]] [INFO] eHLWiRoqqS=running
[[18:47:56]] [INFO] Executing action 356/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[18:47:56]] [SUCCESS] Screenshot refreshed
[[18:47:56]] [INFO] Refreshing screenshot...
[[18:47:56]] [INFO] F4NGh9HrLw=pass
[[18:47:52]] [SUCCESS] Screenshot refreshed successfully
[[18:47:52]] [SUCCESS] Screenshot refreshed successfully
[[18:47:52]] [INFO] F4NGh9HrLw=running
[[18:47:52]] [INFO] Executing action 355/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:47:51]] [SUCCESS] Screenshot refreshed
[[18:47:51]] [INFO] Refreshing screenshot...
[[18:47:51]] [INFO] 74XW7x54ad=pass
[[18:47:48]] [SUCCESS] Screenshot refreshed successfully
[[18:47:48]] [SUCCESS] Screenshot refreshed successfully
[[18:47:47]] [INFO] 74XW7x54ad=running
[[18:47:47]] [INFO] Executing action 354/576: Tap on image: env[device-back-img]
[[18:47:46]] [SUCCESS] Screenshot refreshed
[[18:47:46]] [INFO] Refreshing screenshot...
[[18:47:46]] [INFO] xUbWFa8Ok2=pass
[[18:47:43]] [SUCCESS] Screenshot refreshed successfully
[[18:47:43]] [SUCCESS] Screenshot refreshed successfully
[[18:47:42]] [INFO] xUbWFa8Ok2=running
[[18:47:42]] [INFO] Executing action 353/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[18:47:41]] [SUCCESS] Screenshot refreshed
[[18:47:41]] [INFO] Refreshing screenshot...
[[18:47:41]] [INFO] RbNtEW6N9T=pass
[[18:47:39]] [SUCCESS] Screenshot refreshed successfully
[[18:47:39]] [SUCCESS] Screenshot refreshed successfully
[[18:47:37]] [INFO] RbNtEW6N9T=running
[[18:47:37]] [INFO] Executing action 352/576: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[18:47:37]] [SUCCESS] Screenshot refreshed
[[18:47:37]] [INFO] Refreshing screenshot...
[[18:47:37]] [INFO] F4NGh9HrLw=pass
[[18:47:34]] [SUCCESS] Screenshot refreshed successfully
[[18:47:34]] [SUCCESS] Screenshot refreshed successfully
[[18:47:32]] [INFO] F4NGh9HrLw=running
[[18:47:32]] [INFO] Executing action 351/576: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[18:47:32]] [SUCCESS] Screenshot refreshed
[[18:47:32]] [INFO] Refreshing screenshot...
[[18:47:32]] [INFO] RlDZFks4Lc=pass
[[18:47:30]] [SUCCESS] Screenshot refreshed successfully
[[18:47:30]] [SUCCESS] Screenshot refreshed successfully
[[18:47:28]] [INFO] RlDZFks4Lc=running
[[18:47:28]] [INFO] Executing action 350/576: iOS Function: alert_accept
[[18:47:28]] [SUCCESS] Screenshot refreshed
[[18:47:28]] [INFO] Refreshing screenshot...
[[18:47:28]] [INFO] Dzn2Q7JTe0=pass
[[18:47:23]] [SUCCESS] Screenshot refreshed successfully
[[18:47:23]] [SUCCESS] Screenshot refreshed successfully
[[18:47:23]] [INFO] Dzn2Q7JTe0=running
[[18:47:23]] [INFO] Executing action 349/576: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[18:47:22]] [SUCCESS] Screenshot refreshed
[[18:47:22]] [INFO] Refreshing screenshot...
[[18:47:22]] [INFO] H9fy9qcFbZ=pass
[[18:47:09]] [SUCCESS] Screenshot refreshed successfully
[[18:47:09]] [SUCCESS] Screenshot refreshed successfully
[[18:47:08]] [INFO] H9fy9qcFbZ=running
[[18:47:08]] [INFO] Executing action 348/576: Restart app: env[appid]
[[18:47:08]] [SUCCESS] Screenshot refreshed
[[18:47:08]] [INFO] Refreshing screenshot...
[[18:47:08]] [INFO] AeQaElnzUN=pass
[[18:46:42]] [SUCCESS] Screenshot refreshed successfully
[[18:46:42]] [SUCCESS] Screenshot refreshed successfully
[[18:46:41]] [INFO] AeQaElnzUN=running
[[18:46:41]] [INFO] Executing action 347/576: cleanupSteps action
[[18:46:41]] [SUCCESS] Screenshot refreshed
[[18:46:41]] [INFO] Refreshing screenshot...
[[18:46:41]] [INFO] BracBsfa3Y=pass
[[18:46:35]] [SUCCESS] Screenshot refreshed successfully
[[18:46:35]] [SUCCESS] Screenshot refreshed successfully
[[18:46:35]] [INFO] BracBsfa3Y=running
[[18:46:35]] [INFO] Executing action 346/576: Tap on Text: "out"
[[18:46:35]] [SUCCESS] Screenshot refreshed
[[18:46:35]] [INFO] Refreshing screenshot...
[[18:46:35]] [INFO] s6tWdQ5URW=pass
[[18:46:28]] [SUCCESS] Screenshot refreshed successfully
[[18:46:28]] [SUCCESS] Screenshot refreshed successfully
[[18:46:28]] [INFO] s6tWdQ5URW=running
[[18:46:28]] [INFO] Executing action 345/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:46:27]] [SUCCESS] Screenshot refreshed
[[18:46:27]] [INFO] Refreshing screenshot...
[[18:46:27]] [INFO] wNGRrfUjpK=pass
[[18:46:24]] [SUCCESS] Screenshot refreshed successfully
[[18:46:24]] [SUCCESS] Screenshot refreshed successfully
[[18:46:24]] [INFO] wNGRrfUjpK=running
[[18:46:24]] [INFO] Executing action 344/576: Tap on image: env[device-back-img]
[[18:46:23]] [SUCCESS] Screenshot refreshed
[[18:46:23]] [INFO] Refreshing screenshot...
[[18:46:23]] [INFO] BracBsfa3Y=pass
[[18:46:19]] [INFO] BracBsfa3Y=running
[[18:46:19]] [INFO] Executing action 343/576: Tap on Text: "Customer"
[[18:46:19]] [SUCCESS] Screenshot refreshed successfully
[[18:46:19]] [SUCCESS] Screenshot refreshed successfully
[[18:46:18]] [SUCCESS] Screenshot refreshed
[[18:46:18]] [INFO] Refreshing screenshot...
[[18:46:18]] [INFO] H4WfwVU8YP=pass
[[18:46:14]] [SUCCESS] Screenshot refreshed successfully
[[18:46:14]] [SUCCESS] Screenshot refreshed successfully
[[18:46:14]] [INFO] H4WfwVU8YP=running
[[18:46:14]] [INFO] Executing action 342/576: Tap on image: banner-close-updated.png
[[18:46:13]] [SUCCESS] Screenshot refreshed
[[18:46:13]] [INFO] Refreshing screenshot...
[[18:46:13]] [INFO] ePyaYpttQA=pass
[[18:46:10]] [SUCCESS] Screenshot refreshed successfully
[[18:46:10]] [SUCCESS] Screenshot refreshed successfully
[[18:46:10]] [INFO] ePyaYpttQA=running
[[18:46:10]] [INFO] Executing action 341/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[18:46:09]] [SUCCESS] Screenshot refreshed
[[18:46:09]] [INFO] Refreshing screenshot...
[[18:46:09]] [INFO] BracBsfa3Y=pass
[[18:46:04]] [SUCCESS] Screenshot refreshed successfully
[[18:46:04]] [SUCCESS] Screenshot refreshed successfully
[[18:46:04]] [INFO] BracBsfa3Y=running
[[18:46:04]] [INFO] Executing action 340/576: Tap on Text: "Invite"
[[18:46:04]] [SUCCESS] Screenshot refreshed
[[18:46:04]] [INFO] Refreshing screenshot...
[[18:46:04]] [INFO] xVbCNStsOP=pass
[[18:46:00]] [INFO] xVbCNStsOP=running
[[18:46:00]] [INFO] Executing action 339/576: Tap on image: env[device-back-img]
[[18:46:00]] [SUCCESS] Screenshot refreshed successfully
[[18:46:00]] [SUCCESS] Screenshot refreshed successfully
[[18:46:00]] [SUCCESS] Screenshot refreshed
[[18:46:00]] [INFO] Refreshing screenshot...
[[18:46:00]] [INFO] 8kQkC2FGyZ=pass
[[18:45:56]] [SUCCESS] Screenshot refreshed successfully
[[18:45:56]] [SUCCESS] Screenshot refreshed successfully
[[18:45:56]] [INFO] 8kQkC2FGyZ=running
[[18:45:56]] [INFO] Executing action 338/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[18:45:56]] [SUCCESS] Screenshot refreshed
[[18:45:56]] [INFO] Refreshing screenshot...
[[18:45:56]] [INFO] PgjJCrKFYo=pass
[[18:45:51]] [SUCCESS] Screenshot refreshed successfully
[[18:45:51]] [SUCCESS] Screenshot refreshed successfully
[[18:45:51]] [INFO] PgjJCrKFYo=running
[[18:45:51]] [INFO] Executing action 337/576: Tap on Text: "VIC"
[[18:45:50]] [SUCCESS] Screenshot refreshed
[[18:45:50]] [INFO] Refreshing screenshot...
[[18:45:50]] [INFO] 3Si0csRNaw=pass
[[18:45:44]] [SUCCESS] Screenshot refreshed successfully
[[18:45:44]] [SUCCESS] Screenshot refreshed successfully
[[18:45:44]] [INFO] 3Si0csRNaw=running
[[18:45:44]] [INFO] Executing action 336/576: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[18:45:43]] [SUCCESS] Screenshot refreshed
[[18:45:43]] [INFO] Refreshing screenshot...
[[18:45:43]] [INFO] BracBsfa3Y=pass
[[18:45:38]] [SUCCESS] Screenshot refreshed successfully
[[18:45:38]] [SUCCESS] Screenshot refreshed successfully
[[18:45:38]] [INFO] BracBsfa3Y=running
[[18:45:38]] [INFO] Executing action 335/576: Tap on Text: "Nearby"
[[18:45:37]] [SUCCESS] Screenshot refreshed
[[18:45:37]] [INFO] Refreshing screenshot...
[[18:45:37]] [INFO] BracBsfa3Y=pass
[[18:45:33]] [SUCCESS] Screenshot refreshed successfully
[[18:45:33]] [SUCCESS] Screenshot refreshed successfully
[[18:45:33]] [INFO] BracBsfa3Y=running
[[18:45:33]] [INFO] Executing action 334/576: Tap on Text: "locator"
[[18:45:32]] [SUCCESS] Screenshot refreshed
[[18:45:32]] [INFO] Refreshing screenshot...
[[18:45:32]] [INFO] s6tWdQ5URW=pass
[[18:45:26]] [SUCCESS] Screenshot refreshed successfully
[[18:45:26]] [SUCCESS] Screenshot refreshed successfully
[[18:45:26]] [INFO] s6tWdQ5URW=running
[[18:45:26]] [INFO] Executing action 333/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:45:25]] [SUCCESS] Screenshot refreshed
[[18:45:25]] [INFO] Refreshing screenshot...
[[18:45:25]] [INFO] 2M0KHOVecv=pass
[[18:45:22]] [SUCCESS] Screenshot refreshed successfully
[[18:45:22]] [SUCCESS] Screenshot refreshed successfully
[[18:45:21]] [INFO] 2M0KHOVecv=running
[[18:45:21]] [INFO] Executing action 332/576: Check if element with accessibility_id="txtMy Flybuys card" exists
[[18:45:21]] [SUCCESS] Screenshot refreshed
[[18:45:21]] [INFO] Refreshing screenshot...
[[18:45:21]] [INFO] LBgsj3oLcu=pass
[[18:45:17]] [SUCCESS] Screenshot refreshed successfully
[[18:45:17]] [SUCCESS] Screenshot refreshed successfully
[[18:45:17]] [INFO] LBgsj3oLcu=running
[[18:45:17]] [INFO] Executing action 331/576: Tap on image: env[device-back-img]
[[18:45:16]] [SUCCESS] Screenshot refreshed
[[18:45:16]] [INFO] Refreshing screenshot...
[[18:45:16]] [INFO] biRyWs3nSs=pass
[[18:45:11]] [SUCCESS] Screenshot refreshed successfully
[[18:45:11]] [SUCCESS] Screenshot refreshed successfully
[[18:45:11]] [INFO] biRyWs3nSs=running
[[18:45:11]] [INFO] Executing action 330/576: Tap on element with accessibility_id: btnSaveFlybuysCard
[[18:45:10]] [SUCCESS] Screenshot refreshed
[[18:45:10]] [INFO] Refreshing screenshot...
[[18:45:10]] [INFO] 8cFGh3GD68=pass
[[18:45:04]] [SUCCESS] Screenshot refreshed successfully
[[18:45:04]] [SUCCESS] Screenshot refreshed successfully
[[18:45:04]] [INFO] 8cFGh3GD68=running
[[18:45:04]] [INFO] Executing action 329/576: Tap on element with accessibility_id: Done
[[18:45:03]] [SUCCESS] Screenshot refreshed
[[18:45:03]] [INFO] Refreshing screenshot...
[[18:45:03]] [INFO] sLe0Wurhgm=pass
[[18:45:00]] [SUCCESS] Screenshot refreshed successfully
[[18:45:00]] [SUCCESS] Screenshot refreshed successfully
[[18:45:00]] [INFO] sLe0Wurhgm=running
[[18:45:00]] [INFO] Executing action 328/576: Input text: "2791234567890"
[[18:45:00]] [SUCCESS] Screenshot refreshed
[[18:45:00]] [INFO] Refreshing screenshot...
[[18:45:00]] [INFO] Ey86YRVRzU=pass
[[18:44:54]] [SUCCESS] Screenshot refreshed successfully
[[18:44:54]] [SUCCESS] Screenshot refreshed successfully
[[18:44:54]] [INFO] Ey86YRVRzU=running
[[18:44:54]] [INFO] Executing action 327/576: Tap on element with accessibility_id: Flybuys barcode number
[[18:44:54]] [SUCCESS] Screenshot refreshed
[[18:44:54]] [INFO] Refreshing screenshot...
[[18:44:54]] [INFO] Gxhf3XGc6e=pass
[[18:44:48]] [SUCCESS] Screenshot refreshed successfully
[[18:44:48]] [SUCCESS] Screenshot refreshed successfully
[[18:44:48]] [INFO] Gxhf3XGc6e=running
[[18:44:48]] [INFO] Executing action 326/576: Tap on element with accessibility_id: btnLinkFlyBuys
[[18:44:47]] [SUCCESS] Screenshot refreshed
[[18:44:47]] [INFO] Refreshing screenshot...
[[18:44:47]] [INFO] BracBsfa3Y=pass
[[18:44:43]] [SUCCESS] Screenshot refreshed successfully
[[18:44:43]] [SUCCESS] Screenshot refreshed successfully
[[18:44:43]] [INFO] BracBsfa3Y=running
[[18:44:43]] [INFO] Executing action 325/576: Tap on Text: "Flybuys"
[[18:44:42]] [SUCCESS] Screenshot refreshed
[[18:44:42]] [INFO] Refreshing screenshot...
[[18:44:42]] [INFO] Ds5GfNVb3x=pass
[[18:44:37]] [SUCCESS] Screenshot refreshed successfully
[[18:44:37]] [SUCCESS] Screenshot refreshed successfully
[[18:44:37]] [INFO] Ds5GfNVb3x=running
[[18:44:37]] [INFO] Executing action 324/576: Tap on element with accessibility_id: btnRemove
[[18:44:36]] [SUCCESS] Screenshot refreshed
[[18:44:36]] [INFO] Refreshing screenshot...
[[18:44:36]] [INFO] 3ZFgwFaiXp=pass
[[18:44:31]] [SUCCESS] Screenshot refreshed successfully
[[18:44:31]] [SUCCESS] Screenshot refreshed successfully
[[18:44:30]] [INFO] 3ZFgwFaiXp=running
[[18:44:30]] [INFO] Executing action 323/576: Tap on element with accessibility_id: Remove card
[[18:44:30]] [SUCCESS] Screenshot refreshed
[[18:44:30]] [INFO] Refreshing screenshot...
[[18:44:30]] [INFO] 40hnWPsQ9P=pass
[[18:44:24]] [SUCCESS] Screenshot refreshed successfully
[[18:44:24]] [SUCCESS] Screenshot refreshed successfully
[[18:44:24]] [INFO] 40hnWPsQ9P=running
[[18:44:24]] [INFO] Executing action 322/576: Tap on element with accessibility_id: btneditFlybuysCard
[[18:44:24]] [SUCCESS] Screenshot refreshed
[[18:44:24]] [INFO] Refreshing screenshot...
[[18:44:24]] [INFO] 40hnWPsQ9P=pass
[[18:44:19]] [SUCCESS] Screenshot refreshed successfully
[[18:44:19]] [SUCCESS] Screenshot refreshed successfully
[[18:44:19]] [INFO] 40hnWPsQ9P=running
[[18:44:19]] [INFO] Executing action 321/576: Wait till accessibility_id=btneditFlybuysCard
[[18:44:19]] [SUCCESS] Screenshot refreshed
[[18:44:19]] [INFO] Refreshing screenshot...
[[18:44:19]] [INFO] BracBsfa3Y=pass
[[18:44:14]] [SUCCESS] Screenshot refreshed successfully
[[18:44:14]] [SUCCESS] Screenshot refreshed successfully
[[18:44:14]] [INFO] BracBsfa3Y=running
[[18:44:14]] [INFO] Executing action 320/576: Tap on Text: "Flybuys"
[[18:44:13]] [SUCCESS] Screenshot refreshed
[[18:44:13]] [INFO] Refreshing screenshot...
[[18:44:13]] [INFO] MkTFxfzubv=pass
[[18:44:10]] [SUCCESS] Screenshot refreshed successfully
[[18:44:10]] [SUCCESS] Screenshot refreshed successfully
[[18:44:09]] [INFO] MkTFxfzubv=running
[[18:44:09]] [INFO] Executing action 319/576: Tap on image: env[device-back-img]
[[18:44:09]] [SUCCESS] Screenshot refreshed
[[18:44:09]] [INFO] Refreshing screenshot...
[[18:44:09]] [INFO] EO3cMmdUyM=pass
[[18:44:05]] [SUCCESS] Screenshot refreshed successfully
[[18:44:05]] [SUCCESS] Screenshot refreshed successfully
[[18:44:05]] [INFO] EO3cMmdUyM=running
[[18:44:05]] [INFO] Executing action 318/576: Tap on image: env[device-back-img]
[[18:44:04]] [SUCCESS] Screenshot refreshed
[[18:44:04]] [INFO] Refreshing screenshot...
[[18:44:04]] [INFO] napKDohf3Z=pass
[[18:44:00]] [SUCCESS] Screenshot refreshed successfully
[[18:44:00]] [SUCCESS] Screenshot refreshed successfully
[[18:44:00]] [INFO] napKDohf3Z=running
[[18:44:00]] [INFO] Executing action 317/576: Tap on Text: "payment"
[[18:43:59]] [SUCCESS] Screenshot refreshed
[[18:43:59]] [INFO] Refreshing screenshot...
[[18:43:59]] [INFO] ekqt95ZRol=pass
[[18:43:56]] [SUCCESS] Screenshot refreshed successfully
[[18:43:56]] [SUCCESS] Screenshot refreshed successfully
[[18:43:56]] [INFO] ekqt95ZRol=running
[[18:43:56]] [INFO] Executing action 316/576: Tap on image: env[device-back-img]
[[18:43:55]] [SUCCESS] Screenshot refreshed
[[18:43:55]] [INFO] Refreshing screenshot...
[[18:43:55]] [INFO] 20qUCJgpE9=pass
[[18:43:51]] [SUCCESS] Screenshot refreshed successfully
[[18:43:51]] [SUCCESS] Screenshot refreshed successfully
[[18:43:50]] [INFO] 20qUCJgpE9=running
[[18:43:50]] [INFO] Executing action 315/576: Tap on Text: "address"
[[18:43:50]] [SUCCESS] Screenshot refreshed
[[18:43:50]] [INFO] Refreshing screenshot...
[[18:43:50]] [INFO] 6HR2weiXoT=pass
[[18:43:46]] [SUCCESS] Screenshot refreshed successfully
[[18:43:46]] [SUCCESS] Screenshot refreshed successfully
[[18:43:46]] [INFO] 6HR2weiXoT=running
[[18:43:46]] [INFO] Executing action 314/576: Tap on image: env[device-back-img]
[[18:43:46]] [SUCCESS] Screenshot refreshed
[[18:43:46]] [INFO] Refreshing screenshot...
[[18:43:46]] [INFO] 3hOTINBVMf=pass
[[18:43:41]] [SUCCESS] Screenshot refreshed successfully
[[18:43:41]] [SUCCESS] Screenshot refreshed successfully
[[18:43:41]] [INFO] 3hOTINBVMf=running
[[18:43:41]] [INFO] Executing action 313/576: Tap on Text: "details"
[[18:43:40]] [SUCCESS] Screenshot refreshed
[[18:43:40]] [INFO] Refreshing screenshot...
[[18:43:40]] [INFO] yJi0WxnERj=pass
[[18:43:37]] [SUCCESS] Screenshot refreshed successfully
[[18:43:37]] [SUCCESS] Screenshot refreshed successfully
[[18:43:37]] [INFO] yJi0WxnERj=running
[[18:43:37]] [INFO] Executing action 312/576: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[18:43:36]] [SUCCESS] Screenshot refreshed
[[18:43:36]] [INFO] Refreshing screenshot...
[[18:43:36]] [INFO] PbfHAtFQPP=pass
[[18:43:33]] [SUCCESS] Screenshot refreshed successfully
[[18:43:33]] [SUCCESS] Screenshot refreshed successfully
[[18:43:32]] [INFO] PbfHAtFQPP=running
[[18:43:32]] [INFO] Executing action 311/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:43:32]] [SUCCESS] Screenshot refreshed
[[18:43:32]] [INFO] Refreshing screenshot...
[[18:43:32]] [INFO] 6qZnk86hGg=pass
[[18:43:27]] [SUCCESS] Screenshot refreshed successfully
[[18:43:27]] [SUCCESS] Screenshot refreshed successfully
[[18:43:27]] [INFO] 6qZnk86hGg=running
[[18:43:27]] [INFO] Executing action 310/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:43:27]] [SUCCESS] Screenshot refreshed
[[18:43:27]] [INFO] Refreshing screenshot...
[[18:43:27]] [INFO] FAvQgIuHc1=pass
[[18:43:22]] [SUCCESS] Screenshot refreshed successfully
[[18:43:22]] [SUCCESS] Screenshot refreshed successfully
[[18:43:22]] [INFO] FAvQgIuHc1=running
[[18:43:22]] [INFO] Executing action 309/576: Tap on Text: "Return"
[[18:43:21]] [SUCCESS] Screenshot refreshed
[[18:43:21]] [INFO] Refreshing screenshot...
[[18:43:21]] [INFO] vmc01sHkbr=pass
[[18:43:15]] [SUCCESS] Screenshot refreshed successfully
[[18:43:15]] [SUCCESS] Screenshot refreshed successfully
[[18:43:15]] [INFO] vmc01sHkbr=running
[[18:43:15]] [INFO] Executing action 308/576: Wait for 5 ms
[[18:43:14]] [SUCCESS] Screenshot refreshed
[[18:43:14]] [INFO] Refreshing screenshot...
[[18:43:14]] [INFO] zeu0wd1vcF=pass
[[18:43:01]] [SUCCESS] Screenshot refreshed successfully
[[18:43:01]] [SUCCESS] Screenshot refreshed successfully
[[18:43:01]] [INFO] zeu0wd1vcF=running
[[18:43:01]] [INFO] Executing action 307/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:43:01]] [SUCCESS] Screenshot refreshed
[[18:43:01]] [INFO] Refreshing screenshot...
[[18:43:01]] [INFO] OwWeZes4aT=pass
[[18:42:57]] [SUCCESS] Screenshot refreshed successfully
[[18:42:57]] [SUCCESS] Screenshot refreshed successfully
[[18:42:57]] [INFO] OwWeZes4aT=running
[[18:42:57]] [INFO] Executing action 306/576: Tap on image: env[device-back-img]
[[18:42:56]] [SUCCESS] Screenshot refreshed
[[18:42:56]] [INFO] Refreshing screenshot...
[[18:42:56]] [INFO] aAaTtUE92h=pass
[[18:42:53]] [SUCCESS] Screenshot refreshed successfully
[[18:42:53]] [SUCCESS] Screenshot refreshed successfully
[[18:42:53]] [INFO] aAaTtUE92h=running
[[18:42:53]] [INFO] Executing action 305/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[18:42:53]] [SUCCESS] Screenshot refreshed
[[18:42:53]] [INFO] Refreshing screenshot...
[[18:42:53]] [INFO] 9iOZGMqAZK=pass
[[18:42:49]] [SUCCESS] Screenshot refreshed successfully
[[18:42:49]] [SUCCESS] Screenshot refreshed successfully
[[18:42:49]] [INFO] 9iOZGMqAZK=running
[[18:42:49]] [INFO] Executing action 304/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[18:42:48]] [SUCCESS] Screenshot refreshed
[[18:42:48]] [INFO] Refreshing screenshot...
[[18:42:48]] [INFO] mRTYzOFRRw=pass
[[18:42:45]] [SUCCESS] Screenshot refreshed successfully
[[18:42:45]] [SUCCESS] Screenshot refreshed successfully
[[18:42:45]] [INFO] mRTYzOFRRw=running
[[18:42:45]] [INFO] Executing action 303/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[18:42:45]] [SUCCESS] Screenshot refreshed
[[18:42:45]] [INFO] Refreshing screenshot...
[[18:42:45]] [INFO] 7g6MFJSGIO=pass
[[18:42:41]] [SUCCESS] Screenshot refreshed successfully
[[18:42:41]] [SUCCESS] Screenshot refreshed successfully
[[18:42:41]] [INFO] 7g6MFJSGIO=running
[[18:42:41]] [INFO] Executing action 302/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[18:42:40]] [SUCCESS] Screenshot refreshed
[[18:42:40]] [INFO] Refreshing screenshot...
[[18:42:40]] [INFO] zNwyPagPE1=pass
[[18:42:33]] [SUCCESS] Screenshot refreshed successfully
[[18:42:33]] [SUCCESS] Screenshot refreshed successfully
[[18:42:33]] [INFO] zNwyPagPE1=running
[[18:42:33]] [INFO] Executing action 301/576: Wait for 5 ms
[[18:42:33]] [SUCCESS] Screenshot refreshed
[[18:42:33]] [INFO] Refreshing screenshot...
[[18:42:33]] [INFO] qXsL3wzg6J=pass
[[18:42:29]] [SUCCESS] Screenshot refreshed successfully
[[18:42:29]] [SUCCESS] Screenshot refreshed successfully
[[18:42:29]] [INFO] qXsL3wzg6J=running
[[18:42:29]] [INFO] Executing action 300/576: Tap on image: env[device-back-img]
[[18:42:28]] [SUCCESS] Screenshot refreshed
[[18:42:28]] [INFO] Refreshing screenshot...
[[18:42:28]] [INFO] YuuQe2KupX=pass
[[18:42:24]] [INFO] YuuQe2KupX=running
[[18:42:24]] [INFO] Executing action 299/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[18:42:24]] [SUCCESS] Screenshot refreshed successfully
[[18:42:24]] [SUCCESS] Screenshot refreshed successfully
[[18:42:24]] [SUCCESS] Screenshot refreshed
[[18:42:24]] [INFO] Refreshing screenshot...
[[18:42:24]] [INFO] g0PE7Mofye=pass
[[18:42:18]] [SUCCESS] Screenshot refreshed successfully
[[18:42:18]] [SUCCESS] Screenshot refreshed successfully
[[18:42:18]] [INFO] g0PE7Mofye=running
[[18:42:18]] [INFO] Executing action 298/576: Tap on element with accessibility_id: Print order details
[[18:42:18]] [SUCCESS] Screenshot refreshed
[[18:42:18]] [INFO] Refreshing screenshot...
[[18:42:18]] [INFO] GgQaBLWYkb=pass
[[18:42:14]] [SUCCESS] Screenshot refreshed successfully
[[18:42:14]] [SUCCESS] Screenshot refreshed successfully
[[18:42:14]] [INFO] GgQaBLWYkb=running
[[18:42:14]] [INFO] Executing action 297/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[18:42:13]] [SUCCESS] Screenshot refreshed
[[18:42:13]] [INFO] Refreshing screenshot...
[[18:42:13]] [INFO] f3OrHHzTFN=pass
[[18:41:57]] [SUCCESS] Screenshot refreshed successfully
[[18:41:57]] [SUCCESS] Screenshot refreshed successfully
[[18:41:57]] [INFO] f3OrHHzTFN=running
[[18:41:57]] [INFO] Executing action 296/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[18:41:56]] [SUCCESS] Screenshot refreshed
[[18:41:56]] [INFO] Refreshing screenshot...
[[18:41:56]] [INFO] 7g6MFJSGIO=pass
[[18:41:52]] [SUCCESS] Screenshot refreshed successfully
[[18:41:52]] [SUCCESS] Screenshot refreshed successfully
[[18:41:52]] [INFO] 7g6MFJSGIO=running
[[18:41:52]] [INFO] Executing action 295/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[18:41:51]] [SUCCESS] Screenshot refreshed
[[18:41:51]] [INFO] Refreshing screenshot...
[[18:41:51]] [INFO] Z6g3sGuHTp=pass
[[18:41:45]] [SUCCESS] Screenshot refreshed successfully
[[18:41:45]] [SUCCESS] Screenshot refreshed successfully
[[18:41:45]] [INFO] Z6g3sGuHTp=running
[[18:41:45]] [INFO] Executing action 294/576: Wait for 5 ms
[[18:41:44]] [SUCCESS] Screenshot refreshed
[[18:41:44]] [INFO] Refreshing screenshot...
[[18:41:44]] [INFO] pFlYwTS53v=pass
[[18:41:40]] [SUCCESS] Screenshot refreshed successfully
[[18:41:40]] [SUCCESS] Screenshot refreshed successfully
[[18:41:40]] [INFO] pFlYwTS53v=running
[[18:41:40]] [INFO] Executing action 293/576: Tap on Text: "receipts"
[[18:41:39]] [SUCCESS] Screenshot refreshed
[[18:41:39]] [INFO] Refreshing screenshot...
[[18:41:39]] [INFO] V59u3l1wkM=pass
[[18:41:35]] [SUCCESS] Screenshot refreshed successfully
[[18:41:35]] [SUCCESS] Screenshot refreshed successfully
[[18:41:35]] [INFO] V59u3l1wkM=running
[[18:41:35]] [INFO] Executing action 292/576: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[18:41:35]] [SUCCESS] Screenshot refreshed
[[18:41:35]] [INFO] Refreshing screenshot...
[[18:41:35]] [INFO] sl3Wk1gK8X=pass
[[18:41:33]] [SUCCESS] Screenshot refreshed successfully
[[18:41:33]] [SUCCESS] Screenshot refreshed successfully
[[18:41:30]] [INFO] sl3Wk1gK8X=running
[[18:41:30]] [INFO] Executing action 291/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:41:30]] [SUCCESS] Screenshot refreshed
[[18:41:30]] [INFO] Refreshing screenshot...
[[18:41:30]] [SUCCESS] Screenshot refreshed
[[18:41:30]] [INFO] Refreshing screenshot...
[[18:41:25]] [SUCCESS] Screenshot refreshed successfully
[[18:41:25]] [SUCCESS] Screenshot refreshed successfully
[[18:41:25]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:41:24]] [SUCCESS] Screenshot refreshed
[[18:41:24]] [INFO] Refreshing screenshot...
[[18:41:20]] [SUCCESS] Screenshot refreshed successfully
[[18:41:20]] [SUCCESS] Screenshot refreshed successfully
[[18:41:20]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:41:20]] [SUCCESS] Screenshot refreshed
[[18:41:20]] [INFO] Refreshing screenshot...
[[18:41:15]] [SUCCESS] Screenshot refreshed successfully
[[18:41:15]] [SUCCESS] Screenshot refreshed successfully
[[18:41:15]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[18:41:15]] [SUCCESS] Screenshot refreshed
[[18:41:15]] [INFO] Refreshing screenshot...
[[18:41:10]] [SUCCESS] Screenshot refreshed successfully
[[18:41:10]] [SUCCESS] Screenshot refreshed successfully
[[18:41:10]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:41:10]] [SUCCESS] Screenshot refreshed
[[18:41:10]] [INFO] Refreshing screenshot...
[[18:41:03]] [SUCCESS] Screenshot refreshed successfully
[[18:41:03]] [SUCCESS] Screenshot refreshed successfully
[[18:41:03]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:41:03]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:41:03]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[18:41:03]] [INFO] vjK6GqOF3r=running
[[18:41:03]] [INFO] Executing action 290/576: Execute Test Case: Kmart-Signin (8 steps)
[[18:41:03]] [SUCCESS] Screenshot refreshed
[[18:41:03]] [INFO] Refreshing screenshot...
[[18:41:03]] [INFO] ly2oT3zqmf=pass
[[18:41:00]] [SUCCESS] Screenshot refreshed successfully
[[18:41:00]] [SUCCESS] Screenshot refreshed successfully
[[18:41:00]] [INFO] ly2oT3zqmf=running
[[18:41:00]] [INFO] Executing action 289/576: iOS Function: alert_accept
[[18:40:59]] [SUCCESS] Screenshot refreshed
[[18:40:59]] [INFO] Refreshing screenshot...
[[18:40:59]] [INFO] xAPeBnVHrT=pass
[[18:40:53]] [SUCCESS] Screenshot refreshed successfully
[[18:40:53]] [SUCCESS] Screenshot refreshed successfully
[[18:40:52]] [INFO] xAPeBnVHrT=running
[[18:40:52]] [INFO] Executing action 288/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:40:52]] [SUCCESS] Screenshot refreshed
[[18:40:52]] [INFO] Refreshing screenshot...
[[18:40:52]] [INFO] u6bRYZZFAv=pass
[[18:40:45]] [SUCCESS] Screenshot refreshed successfully
[[18:40:45]] [SUCCESS] Screenshot refreshed successfully
[[18:40:45]] [INFO] u6bRYZZFAv=running
[[18:40:45]] [INFO] Executing action 287/576: Wait for 5 ms
[[18:40:44]] [SUCCESS] Screenshot refreshed
[[18:40:44]] [INFO] Refreshing screenshot...
[[18:40:44]] [INFO] pjFNt3w5Fr=pass
[[18:40:31]] [SUCCESS] Screenshot refreshed successfully
[[18:40:31]] [SUCCESS] Screenshot refreshed successfully
[[18:40:30]] [INFO] pjFNt3w5Fr=running
[[18:40:30]] [INFO] Executing action 286/576: Restart app: env[appid]
[[18:40:29]] [SUCCESS] Screenshot refreshed
[[18:40:29]] [INFO] Refreshing screenshot...
[[18:40:29]] [INFO] PGvsG6rpU4=pass
[[18:40:04]] [SUCCESS] Screenshot refreshed successfully
[[18:40:04]] [SUCCESS] Screenshot refreshed successfully
[[18:40:04]] [INFO] PGvsG6rpU4=running
[[18:40:04]] [INFO] Executing action 285/576: cleanupSteps action
[[18:40:03]] [SUCCESS] Screenshot refreshed
[[18:40:03]] [INFO] Refreshing screenshot...
[[18:40:03]] [INFO] LzGkAcsQyE=pass
[[18:40:00]] [SUCCESS] Screenshot refreshed successfully
[[18:40:00]] [SUCCESS] Screenshot refreshed successfully
[[18:40:00]] [INFO] LzGkAcsQyE=running
[[18:40:00]] [INFO] Executing action 284/576: Terminate app: env[appid]
[[18:39:59]] [SUCCESS] Screenshot refreshed
[[18:39:59]] [INFO] Refreshing screenshot...
[[18:39:59]] [INFO] Bdhe5AoUlM=pass
[[18:39:55]] [SUCCESS] Screenshot refreshed successfully
[[18:39:55]] [SUCCESS] Screenshot refreshed successfully
[[18:39:55]] [INFO] Bdhe5AoUlM=running
[[18:39:55]] [INFO] Executing action 283/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:39:55]] [SUCCESS] Screenshot refreshed
[[18:39:55]] [INFO] Refreshing screenshot...
[[18:39:55]] [INFO] FciJcOsMsB=pass
[[18:39:48]] [SUCCESS] Screenshot refreshed successfully
[[18:39:48]] [SUCCESS] Screenshot refreshed successfully
[[18:39:48]] [INFO] FciJcOsMsB=running
[[18:39:48]] [INFO] Executing action 282/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:39:47]] [SUCCESS] Screenshot refreshed
[[18:39:47]] [INFO] Refreshing screenshot...
[[18:39:47]] [INFO] FARWZvOj0x=pass
[[18:39:44]] [SUCCESS] Screenshot refreshed successfully
[[18:39:44]] [SUCCESS] Screenshot refreshed successfully
[[18:39:43]] [INFO] FARWZvOj0x=running
[[18:39:43]] [INFO] Executing action 281/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:39:43]] [SUCCESS] Screenshot refreshed
[[18:39:43]] [INFO] Refreshing screenshot...
[[18:39:43]] [INFO] bZCkx4U9Gk=pass
[[18:39:38]] [INFO] bZCkx4U9Gk=running
[[18:39:38]] [INFO] Executing action 280/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:39:38]] [SUCCESS] Screenshot refreshed successfully
[[18:39:38]] [SUCCESS] Screenshot refreshed successfully
[[18:39:37]] [SUCCESS] Screenshot refreshed
[[18:39:37]] [INFO] Refreshing screenshot...
[[18:39:37]] [INFO] vwFwkK6ydQ=pass
[[18:39:33]] [SUCCESS] Screenshot refreshed successfully
[[18:39:33]] [SUCCESS] Screenshot refreshed successfully
[[18:39:33]] [INFO] vwFwkK6ydQ=running
[[18:39:33]] [INFO] Executing action 279/576: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[18:39:33]] [SUCCESS] Screenshot refreshed
[[18:39:33]] [INFO] Refreshing screenshot...
[[18:39:33]] [INFO] xLGm9FefWE=pass
[[18:39:28]] [SUCCESS] Screenshot refreshed successfully
[[18:39:28]] [SUCCESS] Screenshot refreshed successfully
[[18:39:28]] [INFO] xLGm9FefWE=running
[[18:39:28]] [INFO] Executing action 278/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[18:39:28]] [SUCCESS] Screenshot refreshed
[[18:39:28]] [INFO] Refreshing screenshot...
[[18:39:28]] [INFO] UtVRXwa86e=pass
[[18:39:21]] [SUCCESS] Screenshot refreshed successfully
[[18:39:21]] [SUCCESS] Screenshot refreshed successfully
[[18:39:21]] [INFO] UtVRXwa86e=running
[[18:39:21]] [INFO] Executing action 277/576: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[18:39:21]] [SUCCESS] Screenshot refreshed
[[18:39:21]] [INFO] Refreshing screenshot...
[[18:39:21]] [INFO] SDtskxyVpg=pass
[[18:39:17]] [SUCCESS] Screenshot refreshed successfully
[[18:39:17]] [SUCCESS] Screenshot refreshed successfully
[[18:39:17]] [INFO] SDtskxyVpg=running
[[18:39:17]] [INFO] Executing action 276/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:39:16]] [SUCCESS] Screenshot refreshed
[[18:39:16]] [INFO] Refreshing screenshot...
[[18:39:16]] [INFO] 6HhScBaqQp=pass
[[18:39:14]] [SUCCESS] Screenshot refreshed successfully
[[18:39:14]] [SUCCESS] Screenshot refreshed successfully
[[18:39:13]] [INFO] 6HhScBaqQp=running
[[18:39:13]] [INFO] Executing action 275/576: iOS Function: alert_accept
[[18:39:13]] [SUCCESS] Screenshot refreshed
[[18:39:13]] [INFO] Refreshing screenshot...
[[18:39:13]] [INFO] quzlwPw42x=pass
[[18:39:09]] [SUCCESS] Screenshot refreshed successfully
[[18:39:09]] [SUCCESS] Screenshot refreshed successfully
[[18:39:07]] [INFO] quzlwPw42x=running
[[18:39:07]] [INFO] Executing action 274/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:39:06]] [SUCCESS] Screenshot refreshed
[[18:39:06]] [INFO] Refreshing screenshot...
[[18:39:06]] [INFO] jQYHQIvQ8l=pass
[[18:39:03]] [SUCCESS] Screenshot refreshed successfully
[[18:39:03]] [SUCCESS] Screenshot refreshed successfully
[[18:39:02]] [INFO] jQYHQIvQ8l=running
[[18:39:02]] [INFO] Executing action 273/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:39:02]] [SUCCESS] Screenshot refreshed
[[18:39:02]] [INFO] Refreshing screenshot...
[[18:39:02]] [INFO] ts3qyFxyMf=pass
[[18:38:57]] [SUCCESS] Screenshot refreshed successfully
[[18:38:57]] [SUCCESS] Screenshot refreshed successfully
[[18:38:57]] [INFO] ts3qyFxyMf=running
[[18:38:57]] [INFO] Executing action 272/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:38:57]] [SUCCESS] Screenshot refreshed
[[18:38:57]] [INFO] Refreshing screenshot...
[[18:38:57]] [INFO] FciJcOsMsB=pass
[[18:38:50]] [SUCCESS] Screenshot refreshed successfully
[[18:38:50]] [SUCCESS] Screenshot refreshed successfully
[[18:38:50]] [INFO] FciJcOsMsB=running
[[18:38:50]] [INFO] Executing action 271/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:38:49]] [SUCCESS] Screenshot refreshed
[[18:38:49]] [INFO] Refreshing screenshot...
[[18:38:49]] [INFO] CWkqGp5ndO=pass
[[18:38:46]] [SUCCESS] Screenshot refreshed successfully
[[18:38:46]] [SUCCESS] Screenshot refreshed successfully
[[18:38:45]] [INFO] CWkqGp5ndO=running
[[18:38:45]] [INFO] Executing action 270/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:38:45]] [SUCCESS] Screenshot refreshed
[[18:38:45]] [INFO] Refreshing screenshot...
[[18:38:45]] [INFO] KfMHchi8cx=pass
[[18:38:38]] [INFO] KfMHchi8cx=running
[[18:38:38]] [INFO] Executing action 269/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:38:38]] [SUCCESS] Screenshot refreshed successfully
[[18:38:38]] [SUCCESS] Screenshot refreshed successfully
[[18:38:37]] [SUCCESS] Screenshot refreshed
[[18:38:37]] [INFO] Refreshing screenshot...
[[18:38:37]] [INFO] zsVeGHiIgX=pass
[[18:38:34]] [INFO] zsVeGHiIgX=running
[[18:38:34]] [INFO] Executing action 268/576: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[18:38:34]] [SUCCESS] Screenshot refreshed successfully
[[18:38:34]] [SUCCESS] Screenshot refreshed successfully
[[18:38:34]] [SUCCESS] Screenshot refreshed
[[18:38:34]] [INFO] Refreshing screenshot...
[[18:38:34]] [INFO] 5nsUXQ5L7u=pass
[[18:38:31]] [INFO] 5nsUXQ5L7u=running
[[18:38:31]] [INFO] Executing action 267/576: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[18:38:30]] [SUCCESS] Screenshot refreshed successfully
[[18:38:30]] [SUCCESS] Screenshot refreshed successfully
[[18:38:30]] [SUCCESS] Screenshot refreshed
[[18:38:30]] [INFO] Refreshing screenshot...
[[18:38:30]] [INFO] iSckENpXrN=pass
[[18:38:27]] [INFO] iSckENpXrN=running
[[18:38:27]] [INFO] Executing action 266/576: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[18:38:27]] [SUCCESS] Screenshot refreshed successfully
[[18:38:27]] [SUCCESS] Screenshot refreshed successfully
[[18:38:26]] [SUCCESS] Screenshot refreshed
[[18:38:26]] [INFO] Refreshing screenshot...
[[18:38:26]] [INFO] J7BPGVnRJI=pass
[[18:38:23]] [INFO] J7BPGVnRJI=running
[[18:38:23]] [INFO] Executing action 265/576: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[18:38:23]] [SUCCESS] Screenshot refreshed successfully
[[18:38:23]] [SUCCESS] Screenshot refreshed successfully
[[18:38:23]] [SUCCESS] Screenshot refreshed
[[18:38:23]] [INFO] Refreshing screenshot...
[[18:38:23]] [INFO] 0pwZCYAtOv=pass
[[18:38:20]] [INFO] 0pwZCYAtOv=running
[[18:38:20]] [INFO] Executing action 264/576: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[18:38:20]] [SUCCESS] Screenshot refreshed successfully
[[18:38:20]] [SUCCESS] Screenshot refreshed successfully
[[18:38:19]] [SUCCESS] Screenshot refreshed
[[18:38:19]] [INFO] Refreshing screenshot...
[[18:38:19]] [INFO] soKM0KayFJ=pass
[[18:38:16]] [INFO] soKM0KayFJ=running
[[18:38:16]] [INFO] Executing action 263/576: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[18:38:16]] [SUCCESS] Screenshot refreshed successfully
[[18:38:16]] [SUCCESS] Screenshot refreshed successfully
[[18:38:16]] [SUCCESS] Screenshot refreshed
[[18:38:16]] [INFO] Refreshing screenshot...
[[18:38:16]] [INFO] hnH3ayslCh=pass
[[18:38:12]] [INFO] hnH3ayslCh=running
[[18:38:12]] [INFO] Executing action 262/576: Tap on Text: "Passcode"
[[18:38:12]] [SUCCESS] Screenshot refreshed successfully
[[18:38:12]] [SUCCESS] Screenshot refreshed successfully
[[18:38:12]] [SUCCESS] Screenshot refreshed
[[18:38:12]] [INFO] Refreshing screenshot...
[[18:38:12]] [INFO] CzVeOTdAX9=pass
[[18:38:00]] [SUCCESS] Screenshot refreshed successfully
[[18:38:00]] [SUCCESS] Screenshot refreshed successfully
[[18:38:00]] [INFO] CzVeOTdAX9=running
[[18:38:00]] [INFO] Executing action 261/576: Wait for 10 ms
[[18:38:00]] [SUCCESS] Screenshot refreshed
[[18:38:00]] [INFO] Refreshing screenshot...
[[18:38:00]] [INFO] NL2gtj6qIu=pass
[[18:37:55]] [SUCCESS] Screenshot refreshed successfully
[[18:37:55]] [SUCCESS] Screenshot refreshed successfully
[[18:37:55]] [INFO] NL2gtj6qIu=running
[[18:37:55]] [INFO] Executing action 260/576: Tap on Text: "Apple"
[[18:37:54]] [SUCCESS] Screenshot refreshed
[[18:37:54]] [INFO] Refreshing screenshot...
[[18:37:54]] [INFO] VsSlyhXuVD=pass
[[18:37:50]] [SUCCESS] Screenshot refreshed successfully
[[18:37:50]] [SUCCESS] Screenshot refreshed successfully
[[18:37:50]] [INFO] VsSlyhXuVD=running
[[18:37:50]] [INFO] Executing action 259/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:37:49]] [SUCCESS] Screenshot refreshed
[[18:37:49]] [INFO] Refreshing screenshot...
[[18:37:49]] [INFO] CJ88OgjKXp=pass
[[18:37:45]] [SUCCESS] Screenshot refreshed successfully
[[18:37:45]] [SUCCESS] Screenshot refreshed successfully
[[18:37:45]] [INFO] CJ88OgjKXp=running
[[18:37:45]] [INFO] Executing action 258/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:37:45]] [SUCCESS] Screenshot refreshed
[[18:37:45]] [INFO] Refreshing screenshot...
[[18:37:45]] [INFO] AYiwFSLTBD=pass
[[18:37:42]] [SUCCESS] Screenshot refreshed successfully
[[18:37:42]] [SUCCESS] Screenshot refreshed successfully
[[18:37:42]] [INFO] AYiwFSLTBD=running
[[18:37:42]] [INFO] Executing action 257/576: iOS Function: alert_accept
[[18:37:41]] [SUCCESS] Screenshot refreshed
[[18:37:41]] [INFO] Refreshing screenshot...
[[18:37:41]] [INFO] HJzOYZNnGr=pass
[[18:37:35]] [SUCCESS] Screenshot refreshed successfully
[[18:37:35]] [SUCCESS] Screenshot refreshed successfully
[[18:37:35]] [INFO] HJzOYZNnGr=running
[[18:37:35]] [INFO] Executing action 256/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:37:34]] [SUCCESS] Screenshot refreshed
[[18:37:34]] [INFO] Refreshing screenshot...
[[18:37:34]] [INFO] taf19mtrUT=pass
[[18:37:30]] [SUCCESS] Screenshot refreshed successfully
[[18:37:30]] [SUCCESS] Screenshot refreshed successfully
[[18:37:30]] [INFO] taf19mtrUT=running
[[18:37:30]] [INFO] Executing action 255/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:37:30]] [SUCCESS] Screenshot refreshed
[[18:37:30]] [INFO] Refreshing screenshot...
[[18:37:30]] [INFO] oiPcknTonJ=pass
[[18:37:25]] [SUCCESS] Screenshot refreshed successfully
[[18:37:25]] [SUCCESS] Screenshot refreshed successfully
[[18:37:25]] [INFO] oiPcknTonJ=running
[[18:37:25]] [INFO] Executing action 254/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:37:25]] [SUCCESS] Screenshot refreshed
[[18:37:25]] [INFO] Refreshing screenshot...
[[18:37:25]] [INFO] FciJcOsMsB=pass
[[18:37:19]] [SUCCESS] Screenshot refreshed successfully
[[18:37:19]] [SUCCESS] Screenshot refreshed successfully
[[18:37:19]] [INFO] FciJcOsMsB=running
[[18:37:19]] [INFO] Executing action 253/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:37:18]] [SUCCESS] Screenshot refreshed
[[18:37:18]] [INFO] Refreshing screenshot...
[[18:37:18]] [INFO] 2qOXZcEmK8=pass
[[18:37:15]] [SUCCESS] Screenshot refreshed successfully
[[18:37:15]] [SUCCESS] Screenshot refreshed successfully
[[18:37:14]] [INFO] 2qOXZcEmK8=running
[[18:37:14]] [INFO] Executing action 252/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:37:14]] [SUCCESS] Screenshot refreshed
[[18:37:14]] [INFO] Refreshing screenshot...
[[18:37:14]] [INFO] M6HdLxu76S=pass
[[18:37:10]] [SUCCESS] Screenshot refreshed successfully
[[18:37:10]] [SUCCESS] Screenshot refreshed successfully
[[18:37:09]] [INFO] M6HdLxu76S=running
[[18:37:09]] [INFO] Executing action 251/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:37:09]] [SUCCESS] Screenshot refreshed
[[18:37:09]] [INFO] Refreshing screenshot...
[[18:37:09]] [INFO] pCPTAtSZbf=pass
[[18:37:04]] [SUCCESS] Screenshot refreshed successfully
[[18:37:04]] [SUCCESS] Screenshot refreshed successfully
[[18:37:04]] [INFO] pCPTAtSZbf=running
[[18:37:04]] [INFO] Executing action 250/576: iOS Function: text - Text: "Wonderbaby@5"
[[18:37:03]] [SUCCESS] Screenshot refreshed
[[18:37:03]] [INFO] Refreshing screenshot...
[[18:37:03]] [INFO] DaVBARRwft=pass
[[18:36:59]] [SUCCESS] Screenshot refreshed successfully
[[18:36:59]] [SUCCESS] Screenshot refreshed successfully
[[18:36:59]] [INFO] DaVBARRwft=running
[[18:36:59]] [INFO] Executing action 249/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[18:36:58]] [SUCCESS] Screenshot refreshed
[[18:36:58]] [INFO] Refreshing screenshot...
[[18:36:58]] [INFO] e1RoZWCZJb=pass
[[18:36:53]] [SUCCESS] Screenshot refreshed successfully
[[18:36:53]] [SUCCESS] Screenshot refreshed successfully
[[18:36:53]] [INFO] e1RoZWCZJb=running
[[18:36:53]] [INFO] Executing action 248/576: iOS Function: text - Text: "<EMAIL>"
[[18:36:53]] [SUCCESS] Screenshot refreshed
[[18:36:53]] [INFO] Refreshing screenshot...
[[18:36:53]] [INFO] y8ZMTkG38M=pass
[[18:36:49]] [SUCCESS] Screenshot refreshed successfully
[[18:36:49]] [SUCCESS] Screenshot refreshed successfully
[[18:36:49]] [INFO] y8ZMTkG38M=running
[[18:36:49]] [INFO] Executing action 247/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[18:36:48]] [SUCCESS] Screenshot refreshed
[[18:36:48]] [INFO] Refreshing screenshot...
[[18:36:48]] [INFO] UUhQjmzfO2=pass
[[18:36:43]] [SUCCESS] Screenshot refreshed successfully
[[18:36:43]] [SUCCESS] Screenshot refreshed successfully
[[18:36:43]] [INFO] UUhQjmzfO2=running
[[18:36:43]] [INFO] Executing action 246/576: Tap on Text: "OnePass"
[[18:36:43]] [SUCCESS] Screenshot refreshed
[[18:36:43]] [INFO] Refreshing screenshot...
[[18:36:43]] [INFO] FciJcOsMsB=pass
[[18:36:38]] [SUCCESS] Screenshot refreshed successfully
[[18:36:38]] [SUCCESS] Screenshot refreshed successfully
[[18:36:38]] [INFO] FciJcOsMsB=running
[[18:36:38]] [INFO] Executing action 245/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:36:37]] [SUCCESS] Screenshot refreshed
[[18:36:37]] [INFO] Refreshing screenshot...
[[18:36:37]] [INFO] NCyuT8W5Xz=pass
[[18:36:33]] [SUCCESS] Screenshot refreshed successfully
[[18:36:33]] [SUCCESS] Screenshot refreshed successfully
[[18:36:33]] [INFO] NCyuT8W5Xz=running
[[18:36:33]] [INFO] Executing action 244/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:36:33]] [SUCCESS] Screenshot refreshed
[[18:36:33]] [INFO] Refreshing screenshot...
[[18:36:33]] [INFO] 2kwu2VBmuZ=pass
[[18:36:30]] [SUCCESS] Screenshot refreshed successfully
[[18:36:30]] [SUCCESS] Screenshot refreshed successfully
[[18:36:30]] [INFO] 2kwu2VBmuZ=running
[[18:36:30]] [INFO] Executing action 243/576: iOS Function: alert_accept
[[18:36:29]] [SUCCESS] Screenshot refreshed
[[18:36:29]] [INFO] Refreshing screenshot...
[[18:36:29]] [INFO] cJDpd7aK3d=pass
[[18:36:24]] [SUCCESS] Screenshot refreshed successfully
[[18:36:24]] [SUCCESS] Screenshot refreshed successfully
[[18:36:23]] [INFO] cJDpd7aK3d=running
[[18:36:23]] [INFO] Executing action 242/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:36:23]] [SUCCESS] Screenshot refreshed
[[18:36:23]] [INFO] Refreshing screenshot...
[[18:36:23]] [INFO] FlEukNkjlS=pass
[[18:36:19]] [SUCCESS] Screenshot refreshed successfully
[[18:36:19]] [SUCCESS] Screenshot refreshed successfully
[[18:36:19]] [INFO] FlEukNkjlS=running
[[18:36:19]] [INFO] Executing action 241/576: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:36:18]] [SUCCESS] Screenshot refreshed
[[18:36:18]] [INFO] Refreshing screenshot...
[[18:36:18]] [INFO] LlRfimKPrn=pass
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:14]] [INFO] LlRfimKPrn=running
[[18:36:14]] [INFO] Executing action 240/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:36:14]] [SUCCESS] Screenshot refreshed
[[18:36:14]] [INFO] Refreshing screenshot...
[[18:36:14]] [INFO] FciJcOsMsB=pass
[[18:36:08]] [SUCCESS] Screenshot refreshed successfully
[[18:36:08]] [SUCCESS] Screenshot refreshed successfully
[[18:36:06]] [INFO] FciJcOsMsB=running
[[18:36:06]] [INFO] Executing action 239/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:36:06]] [SUCCESS] Screenshot refreshed
[[18:36:06]] [INFO] Refreshing screenshot...
[[18:36:06]] [INFO] 08NzsvhQXK=pass
[[18:36:02]] [SUCCESS] Screenshot refreshed successfully
[[18:36:02]] [SUCCESS] Screenshot refreshed successfully
[[18:36:02]] [INFO] 08NzsvhQXK=running
[[18:36:02]] [INFO] Executing action 238/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:36:01]] [SUCCESS] Screenshot refreshed
[[18:36:01]] [INFO] Refreshing screenshot...
[[18:36:01]] [INFO] IsGWxLFpIn=pass
[[18:35:59]] [SUCCESS] Screenshot refreshed successfully
[[18:35:59]] [SUCCESS] Screenshot refreshed successfully
[[18:35:58]] [INFO] IsGWxLFpIn=running
[[18:35:58]] [INFO] Executing action 237/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:35:57]] [SUCCESS] Screenshot refreshed
[[18:35:57]] [INFO] Refreshing screenshot...
[[18:35:57]] [INFO] dyECdbRifp=pass
[[18:35:52]] [SUCCESS] Screenshot refreshed successfully
[[18:35:52]] [SUCCESS] Screenshot refreshed successfully
[[18:35:52]] [INFO] dyECdbRifp=running
[[18:35:52]] [INFO] Executing action 236/576: iOS Function: text - Text: "Wonderbaby@5"
[[18:35:52]] [SUCCESS] Screenshot refreshed
[[18:35:52]] [INFO] Refreshing screenshot...
[[18:35:52]] [INFO] I5bRbYY1hD=pass
[[18:35:48]] [SUCCESS] Screenshot refreshed successfully
[[18:35:48]] [SUCCESS] Screenshot refreshed successfully
[[18:35:48]] [INFO] I5bRbYY1hD=running
[[18:35:48]] [INFO] Executing action 235/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:35:47]] [SUCCESS] Screenshot refreshed
[[18:35:47]] [INFO] Refreshing screenshot...
[[18:35:47]] [INFO] WMl5g82CCq=pass
[[18:35:42]] [SUCCESS] Screenshot refreshed successfully
[[18:35:42]] [SUCCESS] Screenshot refreshed successfully
[[18:35:42]] [INFO] WMl5g82CCq=running
[[18:35:42]] [INFO] Executing action 234/576: iOS Function: text - Text: "<EMAIL>"
[[18:35:42]] [SUCCESS] Screenshot refreshed
[[18:35:42]] [INFO] Refreshing screenshot...
[[18:35:42]] [INFO] 8OsQmoVYqW=pass
[[18:35:38]] [SUCCESS] Screenshot refreshed successfully
[[18:35:38]] [SUCCESS] Screenshot refreshed successfully
[[18:35:38]] [INFO] 8OsQmoVYqW=running
[[18:35:38]] [INFO] Executing action 233/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:35:37]] [SUCCESS] Screenshot refreshed
[[18:35:37]] [INFO] Refreshing screenshot...
[[18:35:37]] [INFO] ImienLpJEN=pass
[[18:35:34]] [SUCCESS] Screenshot refreshed successfully
[[18:35:34]] [SUCCESS] Screenshot refreshed successfully
[[18:35:33]] [INFO] ImienLpJEN=running
[[18:35:33]] [INFO] Executing action 232/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:35:33]] [SUCCESS] Screenshot refreshed
[[18:35:33]] [INFO] Refreshing screenshot...
[[18:35:33]] [INFO] q4hPXCBtx4=pass
[[18:35:31]] [SUCCESS] Screenshot refreshed successfully
[[18:35:31]] [SUCCESS] Screenshot refreshed successfully
[[18:35:30]] [INFO] q4hPXCBtx4=running
[[18:35:30]] [INFO] Executing action 231/576: iOS Function: alert_accept
[[18:35:30]] [SUCCESS] Screenshot refreshed
[[18:35:30]] [INFO] Refreshing screenshot...
[[18:35:30]] [INFO] 2cTZvK1psn=pass
[[18:35:23]] [SUCCESS] Screenshot refreshed successfully
[[18:35:23]] [SUCCESS] Screenshot refreshed successfully
[[18:35:22]] [INFO] 2cTZvK1psn=running
[[18:35:22]] [INFO] Executing action 230/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:35:22]] [SUCCESS] Screenshot refreshed
[[18:35:22]] [INFO] Refreshing screenshot...
[[18:35:22]] [INFO] Vxt7QOYeDD=pass
[[18:35:08]] [SUCCESS] Screenshot refreshed successfully
[[18:35:08]] [SUCCESS] Screenshot refreshed successfully
[[18:35:08]] [INFO] Vxt7QOYeDD=running
[[18:35:08]] [INFO] Executing action 229/576: Restart app: env[appid]
[[18:35:07]] [SUCCESS] Screenshot refreshed
[[18:35:07]] [INFO] Refreshing screenshot...
[[18:35:07]] [INFO] DYWpUY7xB6=pass
[[18:34:41]] [SUCCESS] Screenshot refreshed successfully
[[18:34:41]] [SUCCESS] Screenshot refreshed successfully
[[18:34:40]] [INFO] DYWpUY7xB6=running
[[18:34:40]] [INFO] Executing action 228/576: cleanupSteps action
[[18:34:40]] [SUCCESS] Screenshot refreshed
[[18:34:40]] [INFO] Refreshing screenshot...
[[18:34:40]] [INFO] OyUowAaBzD=pass
[[18:34:35]] [SUCCESS] Screenshot refreshed successfully
[[18:34:35]] [SUCCESS] Screenshot refreshed successfully
[[18:34:35]] [INFO] OyUowAaBzD=running
[[18:34:35]] [INFO] Executing action 227/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:34:34]] [SUCCESS] Screenshot refreshed
[[18:34:34]] [INFO] Refreshing screenshot...
[[18:34:34]] [INFO] Ob26qqcA0p=pass
[[18:34:28]] [SUCCESS] Screenshot refreshed successfully
[[18:34:28]] [SUCCESS] Screenshot refreshed successfully
[[18:34:27]] [INFO] Ob26qqcA0p=running
[[18:34:27]] [INFO] Executing action 226/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:34:27]] [SUCCESS] Screenshot refreshed
[[18:34:27]] [INFO] Refreshing screenshot...
[[18:34:27]] [INFO] k3mu9Mt7Ec=pass
[[18:34:23]] [SUCCESS] Screenshot refreshed successfully
[[18:34:23]] [SUCCESS] Screenshot refreshed successfully
[[18:34:23]] [INFO] k3mu9Mt7Ec=running
[[18:34:23]] [INFO] Executing action 225/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:34:23]] [SUCCESS] Screenshot refreshed
[[18:34:23]] [INFO] Refreshing screenshot...
[[18:34:23]] [INFO] yhmzeynQyu=pass
[[18:34:18]] [SUCCESS] Screenshot refreshed successfully
[[18:34:18]] [SUCCESS] Screenshot refreshed successfully
[[18:34:18]] [INFO] yhmzeynQyu=running
[[18:34:18]] [INFO] Executing action 224/576: Tap on Text: "Remove"
[[18:34:18]] [SUCCESS] Screenshot refreshed
[[18:34:18]] [INFO] Refreshing screenshot...
[[18:34:18]] [INFO] Q0fomJIDoQ=pass
[[18:34:13]] [SUCCESS] Screenshot refreshed successfully
[[18:34:13]] [SUCCESS] Screenshot refreshed successfully
[[18:34:13]] [INFO] Q0fomJIDoQ=running
[[18:34:13]] [INFO] Executing action 223/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:34:12]] [SUCCESS] Screenshot refreshed
[[18:34:12]] [INFO] Refreshing screenshot...
[[18:34:12]] [INFO] yhmzeynQyu=pass
[[18:34:08]] [SUCCESS] Screenshot refreshed successfully
[[18:34:08]] [SUCCESS] Screenshot refreshed successfully
[[18:34:08]] [INFO] yhmzeynQyu=running
[[18:34:08]] [INFO] Executing action 222/576: Tap on Text: "Remove"
[[18:34:07]] [SUCCESS] Screenshot refreshed
[[18:34:07]] [INFO] Refreshing screenshot...
[[18:34:07]] [INFO] Q0fomJIDoQ=pass
[[18:34:01]] [SUCCESS] Screenshot refreshed successfully
[[18:34:01]] [SUCCESS] Screenshot refreshed successfully
[[18:34:01]] [INFO] Q0fomJIDoQ=running
[[18:34:01]] [INFO] Executing action 221/576: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:34:01]] [SUCCESS] Screenshot refreshed
[[18:34:01]] [INFO] Refreshing screenshot...
[[18:34:01]] [INFO] F1olhgKhUt=pass
[[18:33:57]] [SUCCESS] Screenshot refreshed successfully
[[18:33:57]] [SUCCESS] Screenshot refreshed successfully
[[18:33:57]] [INFO] F1olhgKhUt=running
[[18:33:57]] [INFO] Executing action 220/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:33:56]] [SUCCESS] Screenshot refreshed
[[18:33:56]] [INFO] Refreshing screenshot...
[[18:33:56]] [INFO] 8umPSX0vrr=pass
[[18:33:52]] [SUCCESS] Screenshot refreshed successfully
[[18:33:52]] [SUCCESS] Screenshot refreshed successfully
[[18:33:52]] [INFO] 8umPSX0vrr=running
[[18:33:52]] [INFO] Executing action 219/576: Tap on image: banner-close-updated.png
[[18:33:52]] [SUCCESS] Screenshot refreshed
[[18:33:52]] [INFO] Refreshing screenshot...
[[18:33:52]] [INFO] pr9o8Zsm5p=pass
[[18:33:48]] [SUCCESS] Screenshot refreshed successfully
[[18:33:48]] [SUCCESS] Screenshot refreshed successfully
[[18:33:48]] [INFO] pr9o8Zsm5p=running
[[18:33:48]] [INFO] Executing action 218/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[18:33:47]] [SUCCESS] Screenshot refreshed
[[18:33:47]] [INFO] Refreshing screenshot...
[[18:33:47]] [INFO] Qbg9bipTGs=pass
[[18:33:44]] [SUCCESS] Screenshot refreshed successfully
[[18:33:44]] [SUCCESS] Screenshot refreshed successfully
[[18:33:44]] [INFO] Qbg9bipTGs=running
[[18:33:44]] [INFO] Executing action 217/576: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[18:33:43]] [SUCCESS] Screenshot refreshed
[[18:33:43]] [INFO] Refreshing screenshot...
[[18:33:43]] [INFO] Ob26qqcA0p=pass
[[18:33:39]] [INFO] Ob26qqcA0p=running
[[18:33:39]] [INFO] Executing action 216/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:33:38]] [SUCCESS] Screenshot refreshed successfully
[[18:33:38]] [SUCCESS] Screenshot refreshed successfully
[[18:33:38]] [SUCCESS] Screenshot refreshed
[[18:33:38]] [INFO] Refreshing screenshot...
[[18:33:38]] [INFO] lWIRxRm6HE=pass
[[18:33:34]] [SUCCESS] Screenshot refreshed successfully
[[18:33:34]] [SUCCESS] Screenshot refreshed successfully
[[18:33:34]] [INFO] lWIRxRm6HE=running
[[18:33:34]] [INFO] Executing action 215/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:33:34]] [SUCCESS] Screenshot refreshed
[[18:33:34]] [INFO] Refreshing screenshot...
[[18:33:34]] [INFO] uOt2cFGhGr=pass
[[18:33:30]] [SUCCESS] Screenshot refreshed successfully
[[18:33:30]] [SUCCESS] Screenshot refreshed successfully
[[18:33:30]] [INFO] uOt2cFGhGr=running
[[18:33:30]] [INFO] Executing action 214/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:33:29]] [SUCCESS] Screenshot refreshed
[[18:33:29]] [INFO] Refreshing screenshot...
[[18:33:29]] [INFO] Q0fomJIDoQ=pass
[[18:33:25]] [SUCCESS] Screenshot refreshed successfully
[[18:33:25]] [SUCCESS] Screenshot refreshed successfully
[[18:33:25]] [INFO] Q0fomJIDoQ=running
[[18:33:25]] [INFO] Executing action 213/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[18:33:25]] [SUCCESS] Screenshot refreshed
[[18:33:25]] [INFO] Refreshing screenshot...
[[18:33:25]] [INFO] yhmzeynQyu=pass
[[18:33:21]] [SUCCESS] Screenshot refreshed successfully
[[18:33:21]] [SUCCESS] Screenshot refreshed successfully
[[18:33:20]] [INFO] yhmzeynQyu=running
[[18:33:20]] [INFO] Executing action 212/576: Tap on Text: "Remove"
[[18:33:20]] [SUCCESS] Screenshot refreshed
[[18:33:20]] [INFO] Refreshing screenshot...
[[18:33:20]] [INFO] Q0fomJIDoQ=pass
[[18:33:16]] [SUCCESS] Screenshot refreshed successfully
[[18:33:16]] [SUCCESS] Screenshot refreshed successfully
[[18:33:16]] [INFO] Q0fomJIDoQ=running
[[18:33:16]] [INFO] Executing action 211/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[18:33:16]] [SUCCESS] Screenshot refreshed
[[18:33:16]] [INFO] Refreshing screenshot...
[[18:33:16]] [INFO] y4i304JeJj=pass
[[18:33:11]] [SUCCESS] Screenshot refreshed successfully
[[18:33:11]] [SUCCESS] Screenshot refreshed successfully
[[18:33:11]] [INFO] y4i304JeJj=running
[[18:33:11]] [INFO] Executing action 210/576: Tap on Text: "Move"
[[18:33:10]] [SUCCESS] Screenshot refreshed
[[18:33:10]] [INFO] Refreshing screenshot...
[[18:33:10]] [INFO] Q0fomJIDoQ=pass
[[18:33:06]] [SUCCESS] Screenshot refreshed successfully
[[18:33:06]] [SUCCESS] Screenshot refreshed successfully
[[18:33:06]] [INFO] Q0fomJIDoQ=running
[[18:33:06]] [INFO] Executing action 209/576: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:33:05]] [SUCCESS] Screenshot refreshed
[[18:33:05]] [INFO] Refreshing screenshot...
[[18:33:05]] [INFO] Q0fomJIDoQ=pass
[[18:33:02]] [SUCCESS] Screenshot refreshed successfully
[[18:33:02]] [SUCCESS] Screenshot refreshed successfully
[[18:33:02]] [INFO] Q0fomJIDoQ=running
[[18:33:02]] [INFO] Executing action 208/576: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:33:02]] [SUCCESS] Screenshot refreshed
[[18:33:02]] [INFO] Refreshing screenshot...
[[18:33:02]] [INFO] F1olhgKhUt=pass
[[18:32:57]] [SUCCESS] Screenshot refreshed successfully
[[18:32:57]] [SUCCESS] Screenshot refreshed successfully
[[18:32:57]] [INFO] F1olhgKhUt=running
[[18:32:57]] [INFO] Executing action 207/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:32:57]] [SUCCESS] Screenshot refreshed
[[18:32:57]] [INFO] Refreshing screenshot...
[[18:32:57]] [INFO] WbxRVpWtjw=pass
[[18:32:52]] [SUCCESS] Screenshot refreshed successfully
[[18:32:52]] [SUCCESS] Screenshot refreshed successfully
[[18:32:52]] [INFO] WbxRVpWtjw=running
[[18:32:52]] [INFO] Executing action 206/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:32:52]] [SUCCESS] Screenshot refreshed
[[18:32:52]] [INFO] Refreshing screenshot...
[[18:32:52]] [INFO] H3IAmq3r3i=pass
[[18:32:45]] [SUCCESS] Screenshot refreshed successfully
[[18:32:45]] [SUCCESS] Screenshot refreshed successfully
[[18:32:45]] [INFO] H3IAmq3r3i=running
[[18:32:45]] [INFO] Executing action 205/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:32:44]] [SUCCESS] Screenshot refreshed
[[18:32:44]] [INFO] Refreshing screenshot...
[[18:32:44]] [INFO] uOt2cFGhGr=pass
[[18:32:40]] [SUCCESS] Screenshot refreshed successfully
[[18:32:40]] [SUCCESS] Screenshot refreshed successfully
[[18:32:40]] [INFO] uOt2cFGhGr=running
[[18:32:40]] [INFO] Executing action 204/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:32:39]] [SUCCESS] Screenshot refreshed
[[18:32:39]] [INFO] Refreshing screenshot...
[[18:32:39]] [INFO] eLxHVWKeDQ=pass
[[18:32:35]] [SUCCESS] Screenshot refreshed successfully
[[18:32:35]] [SUCCESS] Screenshot refreshed successfully
[[18:32:35]] [INFO] eLxHVWKeDQ=running
[[18:32:35]] [INFO] Executing action 203/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:32:34]] [SUCCESS] Screenshot refreshed
[[18:32:34]] [INFO] Refreshing screenshot...
[[18:32:34]] [INFO] ghzdMuwrHj=pass
[[18:32:30]] [SUCCESS] Screenshot refreshed successfully
[[18:32:30]] [SUCCESS] Screenshot refreshed successfully
[[18:32:30]] [INFO] ghzdMuwrHj=running
[[18:32:30]] [INFO] Executing action 202/576: iOS Function: text - Text: "P_43386093"
[[18:32:29]] [SUCCESS] Screenshot refreshed
[[18:32:29]] [INFO] Refreshing screenshot...
[[18:32:29]] [INFO] fMzoZJg9I7=pass
[[18:32:24]] [SUCCESS] Screenshot refreshed successfully
[[18:32:24]] [SUCCESS] Screenshot refreshed successfully
[[18:32:24]] [INFO] fMzoZJg9I7=running
[[18:32:24]] [INFO] Executing action 201/576: Tap on Text: "Find"
[[18:32:23]] [SUCCESS] Screenshot refreshed
[[18:32:23]] [INFO] Refreshing screenshot...
[[18:32:23]] [INFO] j1JjmfPRaE=pass
[[18:32:18]] [SUCCESS] Screenshot refreshed successfully
[[18:32:18]] [SUCCESS] Screenshot refreshed successfully
[[18:32:18]] [INFO] j1JjmfPRaE=running
[[18:32:18]] [INFO] Executing action 200/576: Restart app: env[appid]
[[18:32:18]] [SUCCESS] Screenshot refreshed
[[18:32:18]] [INFO] Refreshing screenshot...
[[18:32:18]] [INFO] WbxRVpWtjw=pass
[[18:32:14]] [SUCCESS] Screenshot refreshed successfully
[[18:32:14]] [SUCCESS] Screenshot refreshed successfully
[[18:32:14]] [INFO] WbxRVpWtjw=running
[[18:32:14]] [INFO] Executing action 199/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:32:13]] [SUCCESS] Screenshot refreshed
[[18:32:13]] [INFO] Refreshing screenshot...
[[18:32:13]] [INFO] H3IAmq3r3i=pass
[[18:32:06]] [SUCCESS] Screenshot refreshed successfully
[[18:32:06]] [SUCCESS] Screenshot refreshed successfully
[[18:32:06]] [INFO] H3IAmq3r3i=running
[[18:32:06]] [INFO] Executing action 198/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:32:05]] [SUCCESS] Screenshot refreshed
[[18:32:05]] [INFO] Refreshing screenshot...
[[18:32:05]] [INFO] ITHvSyXXmu=pass
[[18:32:02]] [SUCCESS] Screenshot refreshed successfully
[[18:32:02]] [SUCCESS] Screenshot refreshed successfully
[[18:32:01]] [INFO] ITHvSyXXmu=running
[[18:32:01]] [INFO] Executing action 197/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:32:01]] [SUCCESS] Screenshot refreshed
[[18:32:01]] [INFO] Refreshing screenshot...
[[18:32:01]] [INFO] eLxHVWKeDQ=pass
[[18:31:46]] [SUCCESS] Screenshot refreshed successfully
[[18:31:46]] [SUCCESS] Screenshot refreshed successfully
[[18:31:46]] [INFO] eLxHVWKeDQ=running
[[18:31:46]] [INFO] Executing action 196/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[18:31:45]] [SUCCESS] Screenshot refreshed
[[18:31:45]] [INFO] Refreshing screenshot...
[[18:31:45]] [INFO] WbxRVpWtjw=pass
[[18:31:41]] [SUCCESS] Screenshot refreshed successfully
[[18:31:41]] [SUCCESS] Screenshot refreshed successfully
[[18:31:40]] [INFO] WbxRVpWtjw=running
[[18:31:40]] [INFO] Executing action 195/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:31:40]] [SUCCESS] Screenshot refreshed
[[18:31:40]] [INFO] Refreshing screenshot...
[[18:31:40]] [INFO] H3IAmq3r3i=pass
[[18:31:32]] [SUCCESS] Screenshot refreshed successfully
[[18:31:32]] [SUCCESS] Screenshot refreshed successfully
[[18:31:32]] [INFO] H3IAmq3r3i=running
[[18:31:32]] [INFO] Executing action 194/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:31:31]] [SUCCESS] Screenshot refreshed
[[18:31:31]] [INFO] Refreshing screenshot...
[[18:31:31]] [INFO] ITHvSyXXmu=pass
[[18:31:27]] [SUCCESS] Screenshot refreshed successfully
[[18:31:27]] [SUCCESS] Screenshot refreshed successfully
[[18:31:27]] [INFO] ITHvSyXXmu=running
[[18:31:27]] [INFO] Executing action 193/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:31:26]] [SUCCESS] Screenshot refreshed
[[18:31:26]] [INFO] Refreshing screenshot...
[[18:31:26]] [INFO] eLxHVWKeDQ=pass
[[18:31:23]] [SUCCESS] Screenshot refreshed successfully
[[18:31:23]] [SUCCESS] Screenshot refreshed successfully
[[18:31:22]] [INFO] eLxHVWKeDQ=running
[[18:31:22]] [INFO] Executing action 192/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:31:22]] [SUCCESS] Screenshot refreshed
[[18:31:22]] [INFO] Refreshing screenshot...
[[18:31:22]] [INFO] nAB6Q8LAdv=pass
[[18:31:18]] [SUCCESS] Screenshot refreshed successfully
[[18:31:18]] [SUCCESS] Screenshot refreshed successfully
[[18:31:18]] [INFO] nAB6Q8LAdv=running
[[18:31:18]] [INFO] Executing action 191/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:31:17]] [SUCCESS] Screenshot refreshed
[[18:31:17]] [INFO] Refreshing screenshot...
[[18:31:17]] [INFO] sc2KH9bG6H=pass
[[18:31:13]] [SUCCESS] Screenshot refreshed successfully
[[18:31:13]] [SUCCESS] Screenshot refreshed successfully
[[18:31:13]] [INFO] sc2KH9bG6H=running
[[18:31:13]] [INFO] Executing action 190/576: iOS Function: text - Text: "Uno card"
[[18:31:12]] [SUCCESS] Screenshot refreshed
[[18:31:12]] [INFO] Refreshing screenshot...
[[18:31:12]] [INFO] rqLJpAP0mA=pass
[[18:31:06]] [SUCCESS] Screenshot refreshed successfully
[[18:31:06]] [SUCCESS] Screenshot refreshed successfully
[[18:31:06]] [INFO] rqLJpAP0mA=running
[[18:31:06]] [INFO] Executing action 189/576: Tap on Text: "Find"
[[18:31:06]] [SUCCESS] Screenshot refreshed
[[18:31:06]] [INFO] Refreshing screenshot...
[[18:31:06]] [INFO] yiKyF5FJwN=pass
[[18:31:03]] [SUCCESS] Screenshot refreshed successfully
[[18:31:03]] [SUCCESS] Screenshot refreshed successfully
[[18:31:02]] [INFO] yiKyF5FJwN=running
[[18:31:02]] [INFO] Executing action 188/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:31:02]] [SUCCESS] Screenshot refreshed
[[18:31:02]] [INFO] Refreshing screenshot...
[[18:31:02]] [INFO] YqMEb5Jr6o=pass
[[18:30:57]] [SUCCESS] Screenshot refreshed successfully
[[18:30:57]] [SUCCESS] Screenshot refreshed successfully
[[18:30:57]] [INFO] YqMEb5Jr6o=running
[[18:30:57]] [INFO] Executing action 187/576: iOS Function: text - Text: "Wonderbaby@6"
[[18:30:56]] [SUCCESS] Screenshot refreshed
[[18:30:56]] [INFO] Refreshing screenshot...
[[18:30:56]] [INFO] T3MmUw30SF=pass
[[18:30:52]] [SUCCESS] Screenshot refreshed successfully
[[18:30:52]] [SUCCESS] Screenshot refreshed successfully
[[18:30:52]] [INFO] T3MmUw30SF=running
[[18:30:52]] [INFO] Executing action 186/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:30:51]] [SUCCESS] Screenshot refreshed
[[18:30:51]] [INFO] Refreshing screenshot...
[[18:30:51]] [INFO] 3FBGGKUMbh=pass
[[18:30:47]] [SUCCESS] Screenshot refreshed successfully
[[18:30:47]] [SUCCESS] Screenshot refreshed successfully
[[18:30:47]] [INFO] 3FBGGKUMbh=running
[[18:30:47]] [INFO] Executing action 185/576: iOS Function: text - Text: "env[uname-op]"
[[18:30:46]] [SUCCESS] Screenshot refreshed
[[18:30:46]] [INFO] Refreshing screenshot...
[[18:30:46]] [INFO] LDkFLWks00=pass
[[18:30:42]] [SUCCESS] Screenshot refreshed successfully
[[18:30:42]] [SUCCESS] Screenshot refreshed successfully
[[18:30:42]] [INFO] LDkFLWks00=running
[[18:30:42]] [INFO] Executing action 184/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:30:41]] [SUCCESS] Screenshot refreshed
[[18:30:41]] [INFO] Refreshing screenshot...
[[18:30:41]] [INFO] 3caMBvQX7k=pass
[[18:30:38]] [SUCCESS] Screenshot refreshed successfully
[[18:30:38]] [SUCCESS] Screenshot refreshed successfully
[[18:30:38]] [INFO] 3caMBvQX7k=running
[[18:30:38]] [INFO] Executing action 183/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:30:37]] [SUCCESS] Screenshot refreshed
[[18:30:37]] [INFO] Refreshing screenshot...
[[18:30:37]] [INFO] yUJyVO5Wev=pass
[[18:30:34]] [SUCCESS] Screenshot refreshed successfully
[[18:30:34]] [SUCCESS] Screenshot refreshed successfully
[[18:30:34]] [INFO] yUJyVO5Wev=running
[[18:30:34]] [INFO] Executing action 182/576: iOS Function: alert_accept
[[18:30:33]] [SUCCESS] Screenshot refreshed
[[18:30:33]] [INFO] Refreshing screenshot...
[[18:30:33]] [INFO] rkL0oz4kiL=pass
[[18:30:26]] [SUCCESS] Screenshot refreshed successfully
[[18:30:26]] [SUCCESS] Screenshot refreshed successfully
[[18:30:26]] [INFO] rkL0oz4kiL=running
[[18:30:26]] [INFO] Executing action 181/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:30:25]] [SUCCESS] Screenshot refreshed
[[18:30:25]] [INFO] Refreshing screenshot...
[[18:30:25]] [INFO] HotUJOd6oB=pass
[[18:30:12]] [SUCCESS] Screenshot refreshed successfully
[[18:30:12]] [SUCCESS] Screenshot refreshed successfully
[[18:30:11]] [INFO] HotUJOd6oB=running
[[18:30:11]] [INFO] Executing action 180/576: Restart app: env[appid]
[[18:30:11]] [SUCCESS] Screenshot refreshed
[[18:30:11]] [INFO] Refreshing screenshot...
[[18:30:11]] [INFO] IR7wnjW7C8=pass
[[18:29:44]] [SUCCESS] Screenshot refreshed successfully
[[18:29:44]] [SUCCESS] Screenshot refreshed successfully
[[18:29:44]] [INFO] IR7wnjW7C8=running
[[18:29:44]] [INFO] Executing action 179/576: cleanupSteps action
[[18:29:43]] [SUCCESS] Screenshot refreshed
[[18:29:43]] [INFO] Refreshing screenshot...
[[18:29:43]] [INFO] 7WYExJTqjp=pass
[[18:29:39]] [SUCCESS] Screenshot refreshed successfully
[[18:29:39]] [SUCCESS] Screenshot refreshed successfully
[[18:29:39]] [INFO] 7WYExJTqjp=running
[[18:29:39]] [INFO] Executing action 178/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:29:38]] [SUCCESS] Screenshot refreshed
[[18:29:38]] [INFO] Refreshing screenshot...
[[18:29:38]] [INFO] 4WfPFN961S=pass
[[18:29:32]] [SUCCESS] Screenshot refreshed successfully
[[18:29:32]] [SUCCESS] Screenshot refreshed successfully
[[18:29:32]] [INFO] 4WfPFN961S=running
[[18:29:32]] [INFO] Executing action 177/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:29:31]] [SUCCESS] Screenshot refreshed
[[18:29:31]] [INFO] Refreshing screenshot...
[[18:29:31]] [INFO] NurQsFoMkE=pass
[[18:29:27]] [SUCCESS] Screenshot refreshed successfully
[[18:29:27]] [SUCCESS] Screenshot refreshed successfully
[[18:29:27]] [INFO] NurQsFoMkE=running
[[18:29:27]] [INFO] Executing action 176/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:29:27]] [SUCCESS] Screenshot refreshed
[[18:29:27]] [INFO] Refreshing screenshot...
[[18:29:27]] [INFO] CkfAScJNq8=pass
[[18:29:22]] [SUCCESS] Screenshot refreshed successfully
[[18:29:22]] [SUCCESS] Screenshot refreshed successfully
[[18:29:22]] [INFO] CkfAScJNq8=running
[[18:29:22]] [INFO] Executing action 175/576: Tap on image: env[closebtnimage]
[[18:29:22]] [SUCCESS] Screenshot refreshed
[[18:29:22]] [INFO] Refreshing screenshot...
[[18:29:22]] [INFO] 1NWfFsDiTQ=pass
[[18:29:18]] [SUCCESS] Screenshot refreshed successfully
[[18:29:18]] [SUCCESS] Screenshot refreshed successfully
[[18:29:18]] [INFO] 1NWfFsDiTQ=running
[[18:29:18]] [INFO] Executing action 174/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:29:17]] [SUCCESS] Screenshot refreshed
[[18:29:17]] [INFO] Refreshing screenshot...
[[18:29:17]] [INFO] tufIibCj03=pass
[[18:29:13]] [SUCCESS] Screenshot refreshed successfully
[[18:29:13]] [SUCCESS] Screenshot refreshed successfully
[[18:29:13]] [INFO] tufIibCj03=running
[[18:29:13]] [INFO] Executing action 173/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:29:13]] [SUCCESS] Screenshot refreshed
[[18:29:13]] [INFO] Refreshing screenshot...
[[18:29:13]] [INFO] XryN8qR1DX=pass
[[18:29:09]] [SUCCESS] Screenshot refreshed successfully
[[18:29:09]] [SUCCESS] Screenshot refreshed successfully
[[18:29:09]] [INFO] XryN8qR1DX=running
[[18:29:09]] [INFO] Executing action 172/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:29:08]] [SUCCESS] Screenshot refreshed
[[18:29:08]] [INFO] Refreshing screenshot...
[[18:29:08]] [INFO] CkfAScJNq8=pass
[[18:29:04]] [SUCCESS] Screenshot refreshed successfully
[[18:29:04]] [SUCCESS] Screenshot refreshed successfully
[[18:29:04]] [INFO] CkfAScJNq8=running
[[18:29:04]] [INFO] Executing action 171/576: Tap on image: env[closebtnimage]
[[18:29:04]] [SUCCESS] Screenshot refreshed
[[18:29:04]] [INFO] Refreshing screenshot...
[[18:29:04]] [SUCCESS] Screenshot refreshed successfully
[[18:29:04]] [SUCCESS] Screenshot refreshed successfully
[[18:29:03]] [SUCCESS] Screenshot refreshed
[[18:29:03]] [INFO] Refreshing screenshot...
[[18:28:59]] [SUCCESS] Screenshot refreshed successfully
[[18:28:59]] [SUCCESS] Screenshot refreshed successfully
[[18:28:59]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:28:58]] [SUCCESS] Screenshot refreshed
[[18:28:58]] [INFO] Refreshing screenshot...
[[18:28:54]] [SUCCESS] Screenshot refreshed successfully
[[18:28:54]] [SUCCESS] Screenshot refreshed successfully
[[18:28:54]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:28:53]] [SUCCESS] Screenshot refreshed
[[18:28:53]] [INFO] Refreshing screenshot...
[[18:28:49]] [SUCCESS] Screenshot refreshed successfully
[[18:28:49]] [SUCCESS] Screenshot refreshed successfully
[[18:28:49]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[18:28:48]] [SUCCESS] Screenshot refreshed
[[18:28:48]] [INFO] Refreshing screenshot...
[[18:28:44]] [SUCCESS] Screenshot refreshed successfully
[[18:28:44]] [SUCCESS] Screenshot refreshed successfully
[[18:28:44]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:28:43]] [SUCCESS] Screenshot refreshed
[[18:28:43]] [INFO] Refreshing screenshot...
[[18:28:38]] [SUCCESS] Screenshot refreshed successfully
[[18:28:38]] [SUCCESS] Screenshot refreshed successfully
[[18:28:38]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:28:38]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:28:38]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[18:28:38]] [INFO] mWOCt0aAWW=running
[[18:28:38]] [INFO] Executing action 170/576: Execute Test Case: Kmart-Signin (5 steps)
[[18:28:37]] [SUCCESS] Screenshot refreshed
[[18:28:37]] [INFO] Refreshing screenshot...
[[18:28:37]] [INFO] q9ZiyYoE5B=pass
[[18:28:35]] [SUCCESS] Screenshot refreshed successfully
[[18:28:35]] [SUCCESS] Screenshot refreshed successfully
[[18:28:34]] [INFO] q9ZiyYoE5B=running
[[18:28:34]] [INFO] Executing action 169/576: iOS Function: alert_accept
[[18:28:34]] [SUCCESS] Screenshot refreshed
[[18:28:34]] [INFO] Refreshing screenshot...
[[18:28:34]] [INFO] STEdg5jOU8=pass
[[18:28:29]] [SUCCESS] Screenshot refreshed successfully
[[18:28:29]] [SUCCESS] Screenshot refreshed successfully
[[18:28:29]] [INFO] STEdg5jOU8=running
[[18:28:29]] [INFO] Executing action 168/576: Tap on Text: "in"
[[18:28:28]] [SUCCESS] Screenshot refreshed
[[18:28:28]] [INFO] Refreshing screenshot...
[[18:28:28]] [INFO] LDH2hlTZT9=pass
[[18:28:22]] [SUCCESS] Screenshot refreshed successfully
[[18:28:22]] [SUCCESS] Screenshot refreshed successfully
[[18:28:21]] [INFO] LDH2hlTZT9=running
[[18:28:21]] [INFO] Executing action 167/576: Wait for 5 ms
[[18:28:21]] [SUCCESS] Screenshot refreshed
[[18:28:21]] [INFO] Refreshing screenshot...
[[18:28:21]] [INFO] 5Dk9h5bQWl=pass
[[18:28:15]] [SUCCESS] Screenshot refreshed successfully
[[18:28:15]] [SUCCESS] Screenshot refreshed successfully
[[18:28:15]] [INFO] 5Dk9h5bQWl=running
[[18:28:15]] [INFO] Executing action 166/576: Tap on element with accessibility_id: Continue to details
[[18:28:14]] [SUCCESS] Screenshot refreshed
[[18:28:14]] [INFO] Refreshing screenshot...
[[18:28:14]] [INFO] VMzFZ2uTwl=pass
[[18:28:07]] [SUCCESS] Screenshot refreshed successfully
[[18:28:07]] [SUCCESS] Screenshot refreshed successfully
[[18:28:05]] [INFO] VMzFZ2uTwl=running
[[18:28:05]] [INFO] Executing action 165/576: Swipe up till element accessibilityid: "Continue to details" is visible
[[18:28:05]] [SUCCESS] Screenshot refreshed
[[18:28:05]] [INFO] Refreshing screenshot...
[[18:28:05]] [SUCCESS] Screenshot refreshed
[[18:28:05]] [INFO] Refreshing screenshot...
[[18:28:01]] [INFO] Executing Multi Step action step 6/6: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:28:01]] [SUCCESS] Screenshot refreshed successfully
[[18:28:01]] [SUCCESS] Screenshot refreshed successfully
[[18:28:01]] [SUCCESS] Screenshot refreshed
[[18:28:01]] [INFO] Refreshing screenshot...
[[18:27:57]] [SUCCESS] Screenshot refreshed successfully
[[18:27:57]] [SUCCESS] Screenshot refreshed successfully
[[18:27:57]] [INFO] Executing Multi Step action step 5/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:27:56]] [SUCCESS] Screenshot refreshed
[[18:27:56]] [INFO] Refreshing screenshot...
[[18:27:52]] [SUCCESS] Screenshot refreshed successfully
[[18:27:52]] [SUCCESS] Screenshot refreshed successfully
[[18:27:52]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[18:27:51]] [SUCCESS] Screenshot refreshed
[[18:27:51]] [INFO] Refreshing screenshot...
[[18:27:48]] [SUCCESS] Screenshot refreshed successfully
[[18:27:48]] [SUCCESS] Screenshot refreshed successfully
[[18:27:47]] [INFO] Executing Multi Step action step 3/6: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:27:47]] [SUCCESS] Screenshot refreshed
[[18:27:47]] [INFO] Refreshing screenshot...
[[18:27:43]] [SUCCESS] Screenshot refreshed successfully
[[18:27:43]] [SUCCESS] Screenshot refreshed successfully
[[18:27:43]] [INFO] Executing Multi Step action step 2/6: iOS Function: text - Text: "Notebook"
[[18:27:42]] [SUCCESS] Screenshot refreshed
[[18:27:42]] [INFO] Refreshing screenshot...
[[18:27:34]] [SUCCESS] Screenshot refreshed successfully
[[18:27:34]] [SUCCESS] Screenshot refreshed successfully
[[18:27:34]] [INFO] Executing Multi Step action step 1/6: Tap on Text: "Find"
[[18:27:34]] [INFO] Loaded 6 steps from test case: Search and Add (Notebooks)
[[18:27:34]] [INFO] Loading steps for Multi Step action: Search and Add (Notebooks)
[[18:27:34]] [INFO] 8HTspxuvVG=running
[[18:27:34]] [INFO] Executing action 164/576: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[18:27:33]] [SUCCESS] Screenshot refreshed
[[18:27:33]] [INFO] Refreshing screenshot...
[[18:27:33]] [INFO] NurQsFoMkE=pass
[[18:27:30]] [SUCCESS] Screenshot refreshed successfully
[[18:27:30]] [SUCCESS] Screenshot refreshed successfully
[[18:27:29]] [INFO] NurQsFoMkE=running
[[18:27:29]] [INFO] Executing action 163/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:27:29]] [SUCCESS] Screenshot refreshed
[[18:27:29]] [INFO] Refreshing screenshot...
[[18:27:29]] [INFO] 7QpmNS6hif=pass
[[18:27:24]] [SUCCESS] Screenshot refreshed successfully
[[18:27:24]] [SUCCESS] Screenshot refreshed successfully
[[18:27:23]] [INFO] 7QpmNS6hif=running
[[18:27:23]] [INFO] Executing action 162/576: Restart app: env[appid]
[[18:27:23]] [SUCCESS] Screenshot refreshed
[[18:27:23]] [INFO] Refreshing screenshot...
[[18:27:23]] [INFO] 7WYExJTqjp=pass
[[18:27:19]] [SUCCESS] Screenshot refreshed successfully
[[18:27:19]] [SUCCESS] Screenshot refreshed successfully
[[18:27:19]] [INFO] 7WYExJTqjp=running
[[18:27:19]] [INFO] Executing action 161/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:27:18]] [SUCCESS] Screenshot refreshed
[[18:27:18]] [INFO] Refreshing screenshot...
[[18:27:18]] [INFO] 4WfPFN961S=pass
[[18:27:11]] [SUCCESS] Screenshot refreshed successfully
[[18:27:11]] [SUCCESS] Screenshot refreshed successfully
[[18:27:11]] [INFO] 4WfPFN961S=running
[[18:27:11]] [INFO] Executing action 160/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:27:11]] [SUCCESS] Screenshot refreshed
[[18:27:11]] [INFO] Refreshing screenshot...
[[18:27:11]] [INFO] NurQsFoMkE=pass
[[18:27:06]] [SUCCESS] Screenshot refreshed successfully
[[18:27:06]] [SUCCESS] Screenshot refreshed successfully
[[18:27:05]] [INFO] NurQsFoMkE=running
[[18:27:05]] [INFO] Executing action 159/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:27:05]] [SUCCESS] Screenshot refreshed
[[18:27:05]] [INFO] Refreshing screenshot...
[[18:27:05]] [INFO] CkfAScJNq8=pass
[[18:27:01]] [SUCCESS] Screenshot refreshed successfully
[[18:27:01]] [SUCCESS] Screenshot refreshed successfully
[[18:27:01]] [INFO] CkfAScJNq8=running
[[18:27:01]] [INFO] Executing action 158/576: Tap on image: env[closebtnimage]
[[18:27:00]] [SUCCESS] Screenshot refreshed
[[18:27:00]] [INFO] Refreshing screenshot...
[[18:27:00]] [INFO] 1NWfFsDiTQ=pass
[[18:26:56]] [SUCCESS] Screenshot refreshed successfully
[[18:26:56]] [SUCCESS] Screenshot refreshed successfully
[[18:26:56]] [INFO] 1NWfFsDiTQ=running
[[18:26:56]] [INFO] Executing action 157/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:26:56]] [SUCCESS] Screenshot refreshed
[[18:26:56]] [INFO] Refreshing screenshot...
[[18:26:56]] [INFO] tufIibCj03=pass
[[18:26:52]] [SUCCESS] Screenshot refreshed successfully
[[18:26:52]] [SUCCESS] Screenshot refreshed successfully
[[18:26:51]] [INFO] tufIibCj03=running
[[18:26:51]] [INFO] Executing action 156/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:26:51]] [SUCCESS] Screenshot refreshed
[[18:26:51]] [INFO] Refreshing screenshot...
[[18:26:51]] [INFO] g8u66qfKkX=pass
[[18:26:47]] [INFO] g8u66qfKkX=running
[[18:26:47]] [INFO] Executing action 155/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:26:47]] [SUCCESS] Screenshot refreshed successfully
[[18:26:47]] [SUCCESS] Screenshot refreshed successfully
[[18:26:47]] [SUCCESS] Screenshot refreshed
[[18:26:47]] [INFO] Refreshing screenshot...
[[18:26:47]] [INFO] XryN8qR1DX=pass
[[18:26:43]] [SUCCESS] Screenshot refreshed successfully
[[18:26:43]] [SUCCESS] Screenshot refreshed successfully
[[18:26:43]] [INFO] XryN8qR1DX=running
[[18:26:43]] [INFO] Executing action 154/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:26:42]] [SUCCESS] Screenshot refreshed
[[18:26:42]] [INFO] Refreshing screenshot...
[[18:26:42]] [INFO] CkfAScJNq8=pass
[[18:26:38]] [SUCCESS] Screenshot refreshed successfully
[[18:26:38]] [SUCCESS] Screenshot refreshed successfully
[[18:26:38]] [INFO] CkfAScJNq8=running
[[18:26:38]] [INFO] Executing action 153/576: Tap on image: env[closebtnimage]
[[18:26:37]] [SUCCESS] Screenshot refreshed
[[18:26:37]] [INFO] Refreshing screenshot...
[[18:26:37]] [INFO] g8u66qfKkX=pass
[[18:26:30]] [INFO] g8u66qfKkX=running
[[18:26:30]] [INFO] Executing action 152/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:26:30]] [SUCCESS] Screenshot refreshed successfully
[[18:26:30]] [SUCCESS] Screenshot refreshed successfully
[[18:26:30]] [SUCCESS] Screenshot refreshed
[[18:26:30]] [INFO] Refreshing screenshot...
[[18:26:30]] [INFO] pCPTAtSZbf=pass
[[18:26:25]] [SUCCESS] Screenshot refreshed successfully
[[18:26:25]] [SUCCESS] Screenshot refreshed successfully
[[18:26:25]] [INFO] pCPTAtSZbf=running
[[18:26:25]] [INFO] Executing action 151/576: iOS Function: text - Text: "Wonderbaby@5"
[[18:26:25]] [SUCCESS] Screenshot refreshed
[[18:26:25]] [INFO] Refreshing screenshot...
[[18:26:25]] [INFO] DaVBARRwft=pass
[[18:26:21]] [SUCCESS] Screenshot refreshed successfully
[[18:26:21]] [SUCCESS] Screenshot refreshed successfully
[[18:26:20]] [INFO] DaVBARRwft=running
[[18:26:20]] [INFO] Executing action 150/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[18:26:20]] [SUCCESS] Screenshot refreshed
[[18:26:20]] [INFO] Refreshing screenshot...
[[18:26:20]] [INFO] e1RoZWCZJb=pass
[[18:26:15]] [SUCCESS] Screenshot refreshed successfully
[[18:26:15]] [SUCCESS] Screenshot refreshed successfully
[[18:26:15]] [INFO] e1RoZWCZJb=running
[[18:26:15]] [INFO] Executing action 149/576: iOS Function: text - Text: "<EMAIL>"
[[18:26:15]] [SUCCESS] Screenshot refreshed
[[18:26:15]] [INFO] Refreshing screenshot...
[[18:26:15]] [INFO] 50Z2jrodNd=pass
[[18:26:11]] [SUCCESS] Screenshot refreshed successfully
[[18:26:11]] [SUCCESS] Screenshot refreshed successfully
[[18:26:11]] [INFO] 50Z2jrodNd=running
[[18:26:11]] [INFO] Executing action 148/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:26:10]] [SUCCESS] Screenshot refreshed
[[18:26:10]] [INFO] Refreshing screenshot...
[[18:26:10]] [INFO] q9ZiyYoE5B=pass
[[18:26:07]] [SUCCESS] Screenshot refreshed successfully
[[18:26:07]] [SUCCESS] Screenshot refreshed successfully
[[18:26:07]] [INFO] q9ZiyYoE5B=running
[[18:26:07]] [INFO] Executing action 147/576: iOS Function: alert_accept
[[18:26:07]] [SUCCESS] Screenshot refreshed
[[18:26:07]] [INFO] Refreshing screenshot...
[[18:26:07]] [INFO] 6PL8P3rT57=pass
[[18:26:02]] [SUCCESS] Screenshot refreshed successfully
[[18:26:02]] [SUCCESS] Screenshot refreshed successfully
[[18:26:02]] [INFO] 6PL8P3rT57=running
[[18:26:02]] [INFO] Executing action 146/576: Tap on Text: "Sign"
[[18:26:01]] [SUCCESS] Screenshot refreshed
[[18:26:01]] [INFO] Refreshing screenshot...
[[18:26:01]] [INFO] 2YGctqXNED=pass
[[18:25:55]] [SUCCESS] Screenshot refreshed successfully
[[18:25:55]] [SUCCESS] Screenshot refreshed successfully
[[18:25:55]] [INFO] 2YGctqXNED=running
[[18:25:55]] [INFO] Executing action 145/576: Tap on element with accessibility_id: Continue to details
[[18:25:55]] [SUCCESS] Screenshot refreshed
[[18:25:55]] [INFO] Refreshing screenshot...
[[18:25:55]] [INFO] 2YGctqXNED=pass
[[18:25:46]] [SUCCESS] Screenshot refreshed successfully
[[18:25:46]] [SUCCESS] Screenshot refreshed successfully
[[18:25:46]] [INFO] 2YGctqXNED=running
[[18:25:46]] [INFO] Executing action 144/576: Swipe up till element accessibilityid: "Continue to details" is visible
[[18:25:46]] [SUCCESS] Screenshot refreshed
[[18:25:46]] [INFO] Refreshing screenshot...
[[18:25:46]] [INFO] tufIibCj03=pass
[[18:25:42]] [SUCCESS] Screenshot refreshed successfully
[[18:25:42]] [SUCCESS] Screenshot refreshed successfully
[[18:25:42]] [INFO] tufIibCj03=running
[[18:25:42]] [INFO] Executing action 143/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:25:41]] [SUCCESS] Screenshot refreshed
[[18:25:41]] [INFO] Refreshing screenshot...
[[18:25:41]] [INFO] g8u66qfKkX=pass
[[18:25:38]] [SUCCESS] Screenshot refreshed successfully
[[18:25:38]] [SUCCESS] Screenshot refreshed successfully
[[18:25:38]] [INFO] g8u66qfKkX=running
[[18:25:38]] [INFO] Executing action 142/576: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:25:37]] [SUCCESS] Screenshot refreshed
[[18:25:37]] [INFO] Refreshing screenshot...
[[18:25:37]] [INFO] XryN8qR1DX=pass
[[18:25:33]] [SUCCESS] Screenshot refreshed successfully
[[18:25:33]] [SUCCESS] Screenshot refreshed successfully
[[18:25:33]] [INFO] XryN8qR1DX=running
[[18:25:33]] [INFO] Executing action 141/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:25:32]] [SUCCESS] Screenshot refreshed
[[18:25:32]] [INFO] Refreshing screenshot...
[[18:25:32]] [INFO] K2w9XUGwnb=pass
[[18:25:25]] [SUCCESS] Screenshot refreshed successfully
[[18:25:25]] [SUCCESS] Screenshot refreshed successfully
[[18:25:25]] [INFO] K2w9XUGwnb=running
[[18:25:25]] [INFO] Executing action 140/576: Tap on element with accessibility_id: Add to bag
[[18:25:24]] [SUCCESS] Screenshot refreshed
[[18:25:24]] [INFO] Refreshing screenshot...
[[18:25:24]] [INFO] BTYxjEaZEk=pass
[[18:25:20]] [SUCCESS] Screenshot refreshed successfully
[[18:25:20]] [SUCCESS] Screenshot refreshed successfully
[[18:25:20]] [INFO] BTYxjEaZEk=running
[[18:25:20]] [INFO] Executing action 139/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:25:19]] [SUCCESS] Screenshot refreshed
[[18:25:19]] [INFO] Refreshing screenshot...
[[18:25:19]] [INFO] YC6bBrKQgq=pass
[[18:25:16]] [SUCCESS] Screenshot refreshed successfully
[[18:25:16]] [SUCCESS] Screenshot refreshed successfully
[[18:25:15]] [INFO] YC6bBrKQgq=running
[[18:25:15]] [INFO] Executing action 138/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:25:15]] [SUCCESS] Screenshot refreshed
[[18:25:15]] [INFO] Refreshing screenshot...
[[18:25:15]] [INFO] aRgHcQcLDP=pass
[[18:25:11]] [SUCCESS] Screenshot refreshed successfully
[[18:25:11]] [SUCCESS] Screenshot refreshed successfully
[[18:25:11]] [INFO] aRgHcQcLDP=running
[[18:25:11]] [INFO] Executing action 137/576: iOS Function: text - Text: "uno card"
[[18:25:10]] [SUCCESS] Screenshot refreshed
[[18:25:10]] [INFO] Refreshing screenshot...
[[18:25:10]] [INFO] 4PZC1vVWJW=pass
[[18:25:05]] [SUCCESS] Screenshot refreshed successfully
[[18:25:05]] [SUCCESS] Screenshot refreshed successfully
[[18:25:04]] [INFO] 4PZC1vVWJW=running
[[18:25:04]] [INFO] Executing action 136/576: Tap on Text: "Find"
[[18:25:04]] [SUCCESS] Screenshot refreshed
[[18:25:04]] [INFO] Refreshing screenshot...
[[18:25:04]] [INFO] XryN8qR1DX=pass
[[18:25:00]] [SUCCESS] Screenshot refreshed successfully
[[18:25:00]] [SUCCESS] Screenshot refreshed successfully
[[18:25:00]] [INFO] XryN8qR1DX=running
[[18:25:00]] [INFO] Executing action 135/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:24:59]] [SUCCESS] Screenshot refreshed
[[18:24:59]] [INFO] Refreshing screenshot...
[[18:24:59]] [INFO] 7WYExJTqjp=pass
[[18:24:55]] [SUCCESS] Screenshot refreshed successfully
[[18:24:55]] [SUCCESS] Screenshot refreshed successfully
[[18:24:55]] [INFO] 7WYExJTqjp=running
[[18:24:55]] [INFO] Executing action 134/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:24:54]] [SUCCESS] Screenshot refreshed
[[18:24:54]] [INFO] Refreshing screenshot...
[[18:24:54]] [INFO] 4WfPFN961S=pass
[[18:24:48]] [SUCCESS] Screenshot refreshed successfully
[[18:24:48]] [SUCCESS] Screenshot refreshed successfully
[[18:24:47]] [INFO] 4WfPFN961S=running
[[18:24:47]] [INFO] Executing action 133/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:24:47]] [SUCCESS] Screenshot refreshed
[[18:24:47]] [INFO] Refreshing screenshot...
[[18:24:47]] [INFO] NurQsFoMkE=pass
[[18:24:44]] [SUCCESS] Screenshot refreshed successfully
[[18:24:44]] [SUCCESS] Screenshot refreshed successfully
[[18:24:43]] [INFO] NurQsFoMkE=running
[[18:24:43]] [INFO] Executing action 132/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:24:43]] [SUCCESS] Screenshot refreshed
[[18:24:43]] [INFO] Refreshing screenshot...
[[18:24:43]] [SUCCESS] Screenshot refreshed
[[18:24:43]] [INFO] Refreshing screenshot...
[[18:24:38]] [SUCCESS] Screenshot refreshed successfully
[[18:24:38]] [SUCCESS] Screenshot refreshed successfully
[[18:24:38]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:24:37]] [SUCCESS] Screenshot refreshed
[[18:24:37]] [INFO] Refreshing screenshot...
[[18:24:33]] [SUCCESS] Screenshot refreshed successfully
[[18:24:33]] [SUCCESS] Screenshot refreshed successfully
[[18:24:33]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:24:33]] [SUCCESS] Screenshot refreshed
[[18:24:33]] [INFO] Refreshing screenshot...
[[18:24:28]] [SUCCESS] Screenshot refreshed successfully
[[18:24:28]] [SUCCESS] Screenshot refreshed successfully
[[18:24:28]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[18:24:28]] [SUCCESS] Screenshot refreshed
[[18:24:28]] [INFO] Refreshing screenshot...
[[18:24:24]] [SUCCESS] Screenshot refreshed successfully
[[18:24:24]] [SUCCESS] Screenshot refreshed successfully
[[18:24:23]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:24:23]] [SUCCESS] Screenshot refreshed
[[18:24:23]] [INFO] Refreshing screenshot...
[[18:24:17]] [SUCCESS] Screenshot refreshed successfully
[[18:24:17]] [SUCCESS] Screenshot refreshed successfully
[[18:24:17]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:24:17]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:24:17]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[18:24:17]] [INFO] mWOCt0aAWW=running
[[18:24:17]] [INFO] Executing action 131/576: Execute Test Case: Kmart-Signin (5 steps)
[[18:24:17]] [SUCCESS] Screenshot refreshed
[[18:24:17]] [INFO] Refreshing screenshot...
[[18:24:17]] [INFO] byEe7qbCpq=pass
[[18:24:14]] [SUCCESS] Screenshot refreshed successfully
[[18:24:14]] [SUCCESS] Screenshot refreshed successfully
[[18:24:14]] [INFO] byEe7qbCpq=running
[[18:24:14]] [INFO] Executing action 130/576: iOS Function: alert_accept
[[18:24:13]] [SUCCESS] Screenshot refreshed
[[18:24:13]] [INFO] Refreshing screenshot...
[[18:24:13]] [INFO] L6wTorOX8B=pass
[[18:24:09]] [SUCCESS] Screenshot refreshed successfully
[[18:24:09]] [SUCCESS] Screenshot refreshed successfully
[[18:24:09]] [INFO] L6wTorOX8B=running
[[18:24:09]] [INFO] Executing action 129/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[18:24:09]] [SUCCESS] Screenshot refreshed
[[18:24:09]] [INFO] Refreshing screenshot...
[[18:24:09]] [INFO] XryN8qR1DX=pass
[[18:24:05]] [SUCCESS] Screenshot refreshed successfully
[[18:24:05]] [SUCCESS] Screenshot refreshed successfully
[[18:24:04]] [INFO] XryN8qR1DX=running
[[18:24:04]] [INFO] Executing action 128/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:24:04]] [SUCCESS] Screenshot refreshed
[[18:24:04]] [INFO] Refreshing screenshot...
[[18:24:04]] [INFO] lCSewtjn1z=pass
[[18:23:59]] [SUCCESS] Screenshot refreshed successfully
[[18:23:59]] [SUCCESS] Screenshot refreshed successfully
[[18:23:59]] [INFO] lCSewtjn1z=running
[[18:23:59]] [INFO] Executing action 127/576: Restart app: env[appid]
[[18:23:58]] [SUCCESS] Screenshot refreshed
[[18:23:58]] [INFO] Refreshing screenshot...
[[18:23:58]] [INFO] IJh702cxG0=pass
[[18:23:54]] [SUCCESS] Screenshot refreshed successfully
[[18:23:54]] [SUCCESS] Screenshot refreshed successfully
[[18:23:54]] [INFO] IJh702cxG0=running
[[18:23:54]] [INFO] Executing action 126/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:23:53]] [SUCCESS] Screenshot refreshed
[[18:23:53]] [INFO] Refreshing screenshot...
[[18:23:53]] [INFO] 4WfPFN961S=pass
[[18:23:47]] [SUCCESS] Screenshot refreshed successfully
[[18:23:47]] [SUCCESS] Screenshot refreshed successfully
[[18:23:47]] [INFO] 4WfPFN961S=running
[[18:23:47]] [INFO] Executing action 125/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:23:46]] [SUCCESS] Screenshot refreshed
[[18:23:46]] [INFO] Refreshing screenshot...
[[18:23:46]] [INFO] AOcOOSuOsB=pass
[[18:23:42]] [SUCCESS] Screenshot refreshed successfully
[[18:23:42]] [SUCCESS] Screenshot refreshed successfully
[[18:23:42]] [INFO] AOcOOSuOsB=running
[[18:23:42]] [INFO] Executing action 124/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:23:41]] [SUCCESS] Screenshot refreshed
[[18:23:41]] [INFO] Refreshing screenshot...
[[18:23:41]] [INFO] AOcOOSuOsB=pass
[[18:23:35]] [SUCCESS] Screenshot refreshed successfully
[[18:23:35]] [SUCCESS] Screenshot refreshed successfully
[[18:23:35]] [INFO] AOcOOSuOsB=running
[[18:23:35]] [INFO] Executing action 123/576: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:23:34]] [SUCCESS] Screenshot refreshed
[[18:23:34]] [INFO] Refreshing screenshot...
[[18:23:34]] [INFO] N2yjynioko=pass
[[18:23:30]] [SUCCESS] Screenshot refreshed successfully
[[18:23:30]] [SUCCESS] Screenshot refreshed successfully
[[18:23:30]] [INFO] N2yjynioko=running
[[18:23:30]] [INFO] Executing action 122/576: iOS Function: text - Text: "Wonderbaby@5"
[[18:23:29]] [SUCCESS] Screenshot refreshed
[[18:23:29]] [INFO] Refreshing screenshot...
[[18:23:29]] [INFO] SHaIduBnay=pass
[[18:23:25]] [SUCCESS] Screenshot refreshed successfully
[[18:23:25]] [SUCCESS] Screenshot refreshed successfully
[[18:23:25]] [INFO] SHaIduBnay=running
[[18:23:25]] [INFO] Executing action 121/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[18:23:24]] [SUCCESS] Screenshot refreshed
[[18:23:24]] [INFO] Refreshing screenshot...
[[18:23:24]] [INFO] wuIMlAwYVA=pass
[[18:23:19]] [SUCCESS] Screenshot refreshed successfully
[[18:23:19]] [SUCCESS] Screenshot refreshed successfully
[[18:23:19]] [INFO] wuIMlAwYVA=running
[[18:23:19]] [INFO] Executing action 120/576: iOS Function: text - Text: "env[uname1]"
[[18:23:19]] [SUCCESS] Screenshot refreshed
[[18:23:19]] [INFO] Refreshing screenshot...
[[18:23:19]] [INFO] 50Z2jrodNd=pass
[[18:23:15]] [SUCCESS] Screenshot refreshed successfully
[[18:23:15]] [SUCCESS] Screenshot refreshed successfully
[[18:23:14]] [INFO] 50Z2jrodNd=running
[[18:23:14]] [INFO] Executing action 119/576: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[18:23:14]] [SUCCESS] Screenshot refreshed
[[18:23:14]] [INFO] Refreshing screenshot...
[[18:23:14]] [INFO] VK2oI6mXSB=pass
[[18:23:10]] [SUCCESS] Screenshot refreshed successfully
[[18:23:10]] [SUCCESS] Screenshot refreshed successfully
[[18:23:10]] [INFO] VK2oI6mXSB=running
[[18:23:10]] [INFO] Executing action 118/576: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[18:23:10]] [SUCCESS] Screenshot refreshed
[[18:23:10]] [INFO] Refreshing screenshot...
[[18:23:10]] [INFO] q9ZiyYoE5B=pass
[[18:23:07]] [SUCCESS] Screenshot refreshed successfully
[[18:23:07]] [SUCCESS] Screenshot refreshed successfully
[[18:23:07]] [INFO] q9ZiyYoE5B=running
[[18:23:07]] [INFO] Executing action 117/576: iOS Function: alert_accept
[[18:23:06]] [SUCCESS] Screenshot refreshed
[[18:23:06]] [INFO] Refreshing screenshot...
[[18:23:06]] [INFO] 4PZC1vVWJW=pass
[[18:23:01]] [SUCCESS] Screenshot refreshed successfully
[[18:23:01]] [SUCCESS] Screenshot refreshed successfully
[[18:23:01]] [INFO] 4PZC1vVWJW=running
[[18:23:01]] [INFO] Executing action 116/576: Tap on Text: "Sign"
[[18:23:00]] [SUCCESS] Screenshot refreshed
[[18:23:00]] [INFO] Refreshing screenshot...
[[18:23:00]] [INFO] 2YGctqXNED=pass
[[18:22:42]] [SUCCESS] Screenshot refreshed successfully
[[18:22:42]] [SUCCESS] Screenshot refreshed successfully
[[18:22:42]] [INFO] 2YGctqXNED=running
[[18:22:42]] [INFO] Executing action 115/576: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[18:22:41]] [SUCCESS] Screenshot refreshed
[[18:22:41]] [INFO] Refreshing screenshot...
[[18:22:41]] [INFO] 6zUBxjSFym=pass
[[18:22:38]] [SUCCESS] Screenshot refreshed successfully
[[18:22:38]] [SUCCESS] Screenshot refreshed successfully
[[18:22:37]] [INFO] 6zUBxjSFym=running
[[18:22:37]] [INFO] Executing action 114/576: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:22:37]] [SUCCESS] Screenshot refreshed
[[18:22:37]] [INFO] Refreshing screenshot...
[[18:22:37]] [INFO] BTYxjEaZEk=pass
[[18:22:32]] [SUCCESS] Screenshot refreshed successfully
[[18:22:32]] [SUCCESS] Screenshot refreshed successfully
[[18:22:32]] [INFO] BTYxjEaZEk=running
[[18:22:32]] [INFO] Executing action 113/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:22:31]] [SUCCESS] Screenshot refreshed
[[18:22:31]] [INFO] Refreshing screenshot...
[[18:22:31]] [INFO] YC6bBrKQgq=pass
[[18:22:28]] [SUCCESS] Screenshot refreshed successfully
[[18:22:28]] [SUCCESS] Screenshot refreshed successfully
[[18:22:28]] [INFO] YC6bBrKQgq=running
[[18:22:28]] [INFO] Executing action 112/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:22:27]] [SUCCESS] Screenshot refreshed
[[18:22:27]] [INFO] Refreshing screenshot...
[[18:22:27]] [INFO] aRgHcQcLDP=pass
[[18:22:23]] [SUCCESS] Screenshot refreshed successfully
[[18:22:23]] [SUCCESS] Screenshot refreshed successfully
[[18:22:23]] [INFO] aRgHcQcLDP=running
[[18:22:23]] [INFO] Executing action 111/576: iOS Function: text - Text: "uno card"
[[18:22:22]] [SUCCESS] Screenshot refreshed
[[18:22:22]] [INFO] Refreshing screenshot...
[[18:22:22]] [INFO] 4PZC1vVWJW=pass
[[18:22:17]] [SUCCESS] Screenshot refreshed successfully
[[18:22:17]] [SUCCESS] Screenshot refreshed successfully
[[18:22:17]] [INFO] 4PZC1vVWJW=running
[[18:22:17]] [INFO] Executing action 110/576: Tap on Text: "Find"
[[18:22:16]] [SUCCESS] Screenshot refreshed
[[18:22:16]] [INFO] Refreshing screenshot...
[[18:22:16]] [INFO] lCSewtjn1z=pass
[[18:22:11]] [SUCCESS] Screenshot refreshed successfully
[[18:22:11]] [SUCCESS] Screenshot refreshed successfully
[[18:22:11]] [INFO] lCSewtjn1z=running
[[18:22:11]] [INFO] Executing action 109/576: Restart app: env[appid]
[[18:22:10]] [SUCCESS] Screenshot refreshed
[[18:22:10]] [INFO] Refreshing screenshot...
[[18:22:10]] [INFO] A1Wz7p1iVG=pass
[[18:22:05]] [SUCCESS] Screenshot refreshed successfully
[[18:22:05]] [SUCCESS] Screenshot refreshed successfully
[[18:22:05]] [INFO] A1Wz7p1iVG=running
[[18:22:05]] [INFO] Executing action 108/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:22:05]] [SUCCESS] Screenshot refreshed
[[18:22:05]] [INFO] Refreshing screenshot...
[[18:22:05]] [INFO] ehyLmdZWP2=pass
[[18:21:58]] [SUCCESS] Screenshot refreshed successfully
[[18:21:58]] [SUCCESS] Screenshot refreshed successfully
[[18:21:58]] [INFO] ehyLmdZWP2=running
[[18:21:58]] [INFO] Executing action 107/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:21:57]] [SUCCESS] Screenshot refreshed
[[18:21:57]] [INFO] Refreshing screenshot...
[[18:21:57]] [INFO] ydRnBBO1vR=pass
[[18:21:54]] [SUCCESS] Screenshot refreshed successfully
[[18:21:54]] [SUCCESS] Screenshot refreshed successfully
[[18:21:53]] [INFO] ydRnBBO1vR=running
[[18:21:53]] [INFO] Executing action 106/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:21:53]] [SUCCESS] Screenshot refreshed
[[18:21:53]] [INFO] Refreshing screenshot...
[[18:21:53]] [INFO] quZwUwj3a8=pass
[[18:21:50]] [SUCCESS] Screenshot refreshed successfully
[[18:21:50]] [SUCCESS] Screenshot refreshed successfully
[[18:21:49]] [INFO] quZwUwj3a8=running
[[18:21:49]] [INFO] Executing action 105/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[18:21:49]] [SUCCESS] Screenshot refreshed
[[18:21:49]] [INFO] Refreshing screenshot...
[[18:21:49]] [INFO] FHRlQXe58T=pass
[[18:21:45]] [SUCCESS] Screenshot refreshed successfully
[[18:21:45]] [SUCCESS] Screenshot refreshed successfully
[[18:21:44]] [INFO] FHRlQXe58T=running
[[18:21:44]] [INFO] Executing action 104/576: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:21:44]] [SUCCESS] Screenshot refreshed
[[18:21:44]] [INFO] Refreshing screenshot...
[[18:21:44]] [INFO] FHRlQXe58T=pass
[[18:21:40]] [SUCCESS] Screenshot refreshed successfully
[[18:21:40]] [SUCCESS] Screenshot refreshed successfully
[[18:21:39]] [INFO] FHRlQXe58T=running
[[18:21:39]] [INFO] Executing action 103/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtStart Shopping"]
[[18:21:39]] [SUCCESS] Screenshot refreshed
[[18:21:39]] [INFO] Refreshing screenshot...
[[18:21:39]] [INFO] N9sXy9WltY=pass
[[18:21:36]] [SUCCESS] Screenshot refreshed successfully
[[18:21:36]] [SUCCESS] Screenshot refreshed successfully
[[18:21:35]] [INFO] N9sXy9WltY=running
[[18:21:35]] [INFO] Executing action 102/576: Check if element with xpath="//XCUIElementTypeButton[@name="txtStart Shopping"]" exists
[[18:21:35]] [SUCCESS] Screenshot refreshed
[[18:21:35]] [INFO] Refreshing screenshot...
[[18:21:35]] [INFO] 8uojw2klHA=pass
[[18:21:31]] [SUCCESS] Screenshot refreshed successfully
[[18:21:31]] [SUCCESS] Screenshot refreshed successfully
[[18:21:31]] [INFO] 8uojw2klHA=running
[[18:21:31]] [INFO] Executing action 101/576: iOS Function: text - Text: "env[pwd]"
[[18:21:30]] [SUCCESS] Screenshot refreshed
[[18:21:30]] [INFO] Refreshing screenshot...
[[18:21:30]] [INFO] SHaIduBnay=pass
[[18:21:26]] [SUCCESS] Screenshot refreshed successfully
[[18:21:26]] [SUCCESS] Screenshot refreshed successfully
[[18:21:26]] [INFO] SHaIduBnay=running
[[18:21:26]] [INFO] Executing action 100/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:21:25]] [SUCCESS] Screenshot refreshed
[[18:21:25]] [INFO] Refreshing screenshot...
[[18:21:25]] [INFO] TGoXyeQtB7=pass
[[18:21:21]] [SUCCESS] Screenshot refreshed successfully
[[18:21:21]] [SUCCESS] Screenshot refreshed successfully
[[18:21:21]] [INFO] TGoXyeQtB7=running
[[18:21:21]] [INFO] Executing action 99/576: iOS Function: text - Text: "env[uname]"
[[18:21:20]] [SUCCESS] Screenshot refreshed
[[18:21:20]] [INFO] Refreshing screenshot...
[[18:21:20]] [INFO] rLCI6NVxSc=pass
[[18:21:16]] [SUCCESS] Screenshot refreshed successfully
[[18:21:16]] [SUCCESS] Screenshot refreshed successfully
[[18:21:16]] [INFO] rLCI6NVxSc=running
[[18:21:16]] [INFO] Executing action 98/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:21:15]] [SUCCESS] Screenshot refreshed
[[18:21:15]] [INFO] Refreshing screenshot...
[[18:21:15]] [INFO] 6mHVWI3j5e=pass
[[18:21:12]] [SUCCESS] Screenshot refreshed successfully
[[18:21:12]] [SUCCESS] Screenshot refreshed successfully
[[18:21:12]] [INFO] 6mHVWI3j5e=running
[[18:21:12]] [INFO] Executing action 97/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:21:11]] [SUCCESS] Screenshot refreshed
[[18:21:11]] [INFO] Refreshing screenshot...
[[18:21:11]] [INFO] rJVGLpLWM3=pass
[[18:21:08]] [SUCCESS] Screenshot refreshed successfully
[[18:21:08]] [SUCCESS] Screenshot refreshed successfully
[[18:21:08]] [INFO] rJVGLpLWM3=running
[[18:21:08]] [INFO] Executing action 96/576: iOS Function: alert_accept
[[18:21:08]] [SUCCESS] Screenshot refreshed
[[18:21:08]] [INFO] Refreshing screenshot...
[[18:21:08]] [INFO] WlISsMf9QA=pass
[[18:21:04]] [SUCCESS] Screenshot refreshed successfully
[[18:21:04]] [SUCCESS] Screenshot refreshed successfully
[[18:21:04]] [INFO] WlISsMf9QA=running
[[18:21:04]] [INFO] Executing action 95/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[18:21:03]] [SUCCESS] Screenshot refreshed
[[18:21:03]] [INFO] Refreshing screenshot...
[[18:21:03]] [INFO] IvqPpScAJa=pass
[[18:21:00]] [SUCCESS] Screenshot refreshed successfully
[[18:21:00]] [SUCCESS] Screenshot refreshed successfully
[[18:21:00]] [INFO] IvqPpScAJa=running
[[18:21:00]] [INFO] Executing action 94/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:20:59]] [SUCCESS] Screenshot refreshed
[[18:20:59]] [INFO] Refreshing screenshot...
[[18:20:59]] [INFO] bGo3feCwBQ=pass
[[18:20:55]] [SUCCESS] Screenshot refreshed successfully
[[18:20:55]] [SUCCESS] Screenshot refreshed successfully
[[18:20:55]] [INFO] bGo3feCwBQ=running
[[18:20:55]] [INFO] Executing action 93/576: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:20:54]] [SUCCESS] Screenshot refreshed
[[18:20:54]] [INFO] Refreshing screenshot...
[[18:20:54]] [INFO] 4WfPFN961S=pass
[[18:20:47]] [SUCCESS] Screenshot refreshed successfully
[[18:20:47]] [SUCCESS] Screenshot refreshed successfully
[[18:20:47]] [INFO] 4WfPFN961S=running
[[18:20:47]] [INFO] Executing action 92/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:20:47]] [SUCCESS] Screenshot refreshed
[[18:20:47]] [INFO] Refreshing screenshot...
[[18:20:47]] [INFO] F0gZF1jEnT=pass
[[18:20:43]] [SUCCESS] Screenshot refreshed successfully
[[18:20:43]] [SUCCESS] Screenshot refreshed successfully
[[18:20:43]] [INFO] F0gZF1jEnT=running
[[18:20:43]] [INFO] Executing action 91/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:20:42]] [SUCCESS] Screenshot refreshed
[[18:20:42]] [INFO] Refreshing screenshot...
[[18:20:42]] [INFO] EDHl0X27Wi=pass
[[18:20:39]] [SUCCESS] Screenshot refreshed successfully
[[18:20:39]] [SUCCESS] Screenshot refreshed successfully
[[18:20:39]] [INFO] EDHl0X27Wi=running
[[18:20:39]] [INFO] Executing action 90/576: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[18:20:38]] [SUCCESS] Screenshot refreshed
[[18:20:38]] [INFO] Refreshing screenshot...
[[18:20:38]] [INFO] j8NXU87gV3=pass
[[18:20:33]] [SUCCESS] Screenshot refreshed successfully
[[18:20:33]] [SUCCESS] Screenshot refreshed successfully
[[18:20:33]] [INFO] j8NXU87gV3=running
[[18:20:33]] [INFO] Executing action 89/576: iOS Function: text - Text: "env[pwd]"
[[18:20:33]] [SUCCESS] Screenshot refreshed
[[18:20:33]] [INFO] Refreshing screenshot...
[[18:20:33]] [INFO] dpVaKL19uc=pass
[[18:20:28]] [SUCCESS] Screenshot refreshed successfully
[[18:20:28]] [SUCCESS] Screenshot refreshed successfully
[[18:20:28]] [INFO] dpVaKL19uc=running
[[18:20:28]] [INFO] Executing action 88/576: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:20:28]] [SUCCESS] Screenshot refreshed
[[18:20:28]] [INFO] Refreshing screenshot...
[[18:20:28]] [INFO] eOm1WExcrK=pass
[[18:20:23]] [SUCCESS] Screenshot refreshed successfully
[[18:20:23]] [SUCCESS] Screenshot refreshed successfully
[[18:20:23]] [INFO] eOm1WExcrK=running
[[18:20:23]] [INFO] Executing action 87/576: iOS Function: text - Text: "env[uname]"
[[18:20:23]] [SUCCESS] Screenshot refreshed
[[18:20:23]] [INFO] Refreshing screenshot...
[[18:20:23]] [INFO] 50Z2jrodNd=pass
[[18:20:19]] [SUCCESS] Screenshot refreshed successfully
[[18:20:19]] [SUCCESS] Screenshot refreshed successfully
[[18:20:18]] [INFO] 50Z2jrodNd=running
[[18:20:18]] [INFO] Executing action 86/576: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:20:18]] [SUCCESS] Screenshot refreshed
[[18:20:18]] [INFO] Refreshing screenshot...
[[18:20:18]] [INFO] eJnHS9n9VL=pass
[[18:20:14]] [SUCCESS] Screenshot refreshed successfully
[[18:20:14]] [SUCCESS] Screenshot refreshed successfully
[[18:20:14]] [INFO] eJnHS9n9VL=running
[[18:20:14]] [INFO] Executing action 85/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:20:14]] [SUCCESS] Screenshot refreshed
[[18:20:14]] [INFO] Refreshing screenshot...
[[18:20:14]] [INFO] XuLgjNG74w=pass
[[18:20:11]] [SUCCESS] Screenshot refreshed successfully
[[18:20:11]] [SUCCESS] Screenshot refreshed successfully
[[18:20:11]] [INFO] XuLgjNG74w=running
[[18:20:11]] [INFO] Executing action 84/576: iOS Function: alert_accept
[[18:20:10]] [SUCCESS] Screenshot refreshed
[[18:20:10]] [INFO] Refreshing screenshot...
[[18:20:10]] [INFO] qA1ap4n1m4=pass
[[18:20:03]] [SUCCESS] Screenshot refreshed successfully
[[18:20:03]] [SUCCESS] Screenshot refreshed successfully
[[18:20:02]] [INFO] qA1ap4n1m4=running
[[18:20:02]] [INFO] Executing action 83/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:20:02]] [SUCCESS] Screenshot refreshed
[[18:20:02]] [INFO] Refreshing screenshot...
[[18:20:02]] [INFO] JXFxYCr98V=pass
[[18:19:48]] [SUCCESS] Screenshot refreshed successfully
[[18:19:48]] [SUCCESS] Screenshot refreshed successfully
[[18:19:48]] [INFO] JXFxYCr98V=running
[[18:19:48]] [INFO] Executing action 82/576: Restart app: env[appid]
[[18:19:47]] [SUCCESS] Screenshot refreshed
[[18:19:47]] [INFO] Refreshing screenshot...
[[18:19:47]] [INFO] hbIlJIWlVN=pass
[[18:19:27]] [INFO] hbIlJIWlVN=running
[[18:19:27]] [INFO] Executing action 81/576: cleanupSteps action
[[18:19:27]] [INFO] Skipping remaining steps in failed test case (moving from action 60 to 80), but preserving cleanup steps
[[18:19:27]] [INFO] sc2KH9bG6H=fail
[[18:19:27]] [ERROR] Action 60 failed: Failed to input text: Uno card
[[18:19:25]] [SUCCESS] Screenshot refreshed successfully
[[18:19:25]] [SUCCESS] Screenshot refreshed successfully
[[18:19:25]] [INFO] sc2KH9bG6H=running
[[18:19:25]] [INFO] Executing action 60/576: iOS Function: text - Text: "Uno card"
[[18:19:24]] [SUCCESS] Screenshot refreshed
[[18:19:24]] [INFO] Refreshing screenshot...
[[18:19:24]] [INFO] ZBXCQNlT8z=pass
[[18:19:20]] [SUCCESS] Screenshot refreshed successfully
[[18:19:20]] [SUCCESS] Screenshot refreshed successfully
[[18:19:19]] [INFO] ZBXCQNlT8z=running
[[18:19:19]] [INFO] Executing action 59/576: Tap on Text: "Find"
[[18:19:19]] [SUCCESS] Screenshot refreshed
[[18:19:19]] [INFO] Refreshing screenshot...
[[18:19:18]] [SUCCESS] Screenshot refreshed
[[18:19:18]] [INFO] Refreshing screenshot...
[[18:19:13]] [SUCCESS] Screenshot refreshed successfully
[[18:19:13]] [SUCCESS] Screenshot refreshed successfully
[[18:19:13]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:19:13]] [SUCCESS] Screenshot refreshed
[[18:19:13]] [INFO] Refreshing screenshot...
[[18:19:09]] [SUCCESS] Screenshot refreshed successfully
[[18:19:09]] [SUCCESS] Screenshot refreshed successfully
[[18:19:09]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:19:08]] [SUCCESS] Screenshot refreshed
[[18:19:08]] [INFO] Refreshing screenshot...
[[18:19:03]] [SUCCESS] Screenshot refreshed successfully
[[18:19:03]] [SUCCESS] Screenshot refreshed successfully
[[18:19:03]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "env[uname]"
[[18:19:03]] [SUCCESS] Screenshot refreshed
[[18:19:03]] [INFO] Refreshing screenshot...
[[18:18:59]] [SUCCESS] Screenshot refreshed successfully
[[18:18:59]] [SUCCESS] Screenshot refreshed successfully
[[18:18:59]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:18:58]] [SUCCESS] Screenshot refreshed
[[18:18:58]] [INFO] Refreshing screenshot...
[[18:18:53]] [SUCCESS] Screenshot refreshed successfully
[[18:18:53]] [SUCCESS] Screenshot refreshed successfully
[[18:18:52]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:18:52]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:18:52]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[18:18:52]] [INFO] El6k4IPZly=running
[[18:18:52]] [INFO] Executing action 58/576: Execute Test Case: Kmart-Signin (8 steps)
[[18:18:52]] [SUCCESS] Screenshot refreshed
[[18:18:52]] [INFO] Refreshing screenshot...
[[18:18:52]] [INFO] 3caMBvQX7k=pass
[[18:18:48]] [SUCCESS] Screenshot refreshed successfully
[[18:18:48]] [SUCCESS] Screenshot refreshed successfully
[[18:18:48]] [INFO] 3caMBvQX7k=running
[[18:18:48]] [INFO] Executing action 57/576: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:18:48]] [SUCCESS] Screenshot refreshed
[[18:18:48]] [INFO] Refreshing screenshot...
[[18:18:48]] [INFO] yUJyVO5Wev=pass
[[18:18:45]] [SUCCESS] Screenshot refreshed successfully
[[18:18:45]] [SUCCESS] Screenshot refreshed successfully
[[18:18:45]] [INFO] yUJyVO5Wev=running
[[18:18:45]] [INFO] Executing action 56/576: iOS Function: alert_accept
[[18:18:44]] [SUCCESS] Screenshot refreshed
[[18:18:44]] [INFO] Refreshing screenshot...
[[18:18:44]] [INFO] rkL0oz4kiL=pass
[[18:18:39]] [SUCCESS] Screenshot refreshed successfully
[[18:18:39]] [SUCCESS] Screenshot refreshed successfully
[[18:18:38]] [INFO] rkL0oz4kiL=running
[[18:18:38]] [INFO] Executing action 55/576: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:18:38]] [SUCCESS] Screenshot refreshed
[[18:18:38]] [INFO] Refreshing screenshot...
[[18:18:38]] [INFO] HotUJOd6oB=pass
[[18:18:24]] [SUCCESS] Screenshot refreshed successfully
[[18:18:24]] [SUCCESS] Screenshot refreshed successfully
[[18:18:24]] [INFO] HotUJOd6oB=running
[[18:18:24]] [INFO] Executing action 54/576: Restart app: env[appid]
[[18:18:23]] [SUCCESS] Screenshot refreshed
[[18:18:23]] [INFO] Refreshing screenshot...
[[18:18:23]] [INFO] vKo6Ox3YrP=pass
[[18:17:58]] [SUCCESS] Screenshot refreshed successfully
[[18:17:58]] [SUCCESS] Screenshot refreshed successfully
[[18:17:57]] [INFO] vKo6Ox3YrP=running
[[18:17:57]] [INFO] Executing action 53/576: cleanupSteps action
[[18:17:56]] [SUCCESS] Screenshot refreshed
[[18:17:56]] [INFO] Refreshing screenshot...
[[18:17:56]] [INFO] x4yLCZHaCR=pass
[[18:17:53]] [SUCCESS] Screenshot refreshed successfully
[[18:17:53]] [SUCCESS] Screenshot refreshed successfully
[[18:17:53]] [INFO] x4yLCZHaCR=running
[[18:17:53]] [INFO] Executing action 52/576: Terminate app: env[appid]
[[18:17:53]] [SUCCESS] Screenshot refreshed
[[18:17:53]] [INFO] Refreshing screenshot...
[[18:17:53]] [INFO] 2p13JoJbbA=pass
[[18:17:49]] [SUCCESS] Screenshot refreshed successfully
[[18:17:49]] [SUCCESS] Screenshot refreshed successfully
[[18:17:48]] [INFO] 2p13JoJbbA=running
[[18:17:48]] [INFO] Executing action 51/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:17:48]] [SUCCESS] Screenshot refreshed
[[18:17:48]] [INFO] Refreshing screenshot...
[[18:17:48]] [INFO] 2p13JoJbbA=pass
[[18:17:44]] [SUCCESS] Screenshot refreshed successfully
[[18:17:44]] [SUCCESS] Screenshot refreshed successfully
[[18:17:44]] [INFO] 2p13JoJbbA=running
[[18:17:44]] [INFO] Executing action 50/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:17:43]] [SUCCESS] Screenshot refreshed
[[18:17:43]] [INFO] Refreshing screenshot...
[[18:17:43]] [INFO] nyBidG0kHp=pass
[[18:17:36]] [INFO] nyBidG0kHp=running
[[18:17:36]] [INFO] Executing action 49/576: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[18:17:36]] [SUCCESS] Screenshot refreshed successfully
[[18:17:36]] [SUCCESS] Screenshot refreshed successfully
[[18:17:36]] [SUCCESS] Screenshot refreshed
[[18:17:36]] [INFO] Refreshing screenshot...
[[18:17:36]] [INFO] F4NGh9HrLw=pass
[[18:17:31]] [SUCCESS] Screenshot refreshed successfully
[[18:17:31]] [SUCCESS] Screenshot refreshed successfully
[[18:17:31]] [INFO] F4NGh9HrLw=running
[[18:17:31]] [INFO] Executing action 48/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:17:31]] [SUCCESS] Screenshot refreshed
[[18:17:31]] [INFO] Refreshing screenshot...
[[18:17:31]] [INFO] VtMfqK1V9t=pass
[[18:17:23]] [SUCCESS] Screenshot refreshed successfully
[[18:17:23]] [SUCCESS] Screenshot refreshed successfully
[[18:17:23]] [INFO] VtMfqK1V9t=running
[[18:17:23]] [INFO] Executing action 47/576: Tap on element with accessibility_id: Add to bag
[[18:17:23]] [SUCCESS] Screenshot refreshed
[[18:17:23]] [INFO] Refreshing screenshot...
[[18:17:23]] [INFO] NOnuFzXy63=pass
[[18:17:19]] [SUCCESS] Screenshot refreshed successfully
[[18:17:19]] [SUCCESS] Screenshot refreshed successfully
[[18:17:18]] [INFO] NOnuFzXy63=running
[[18:17:18]] [INFO] Executing action 46/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:17:18]] [SUCCESS] Screenshot refreshed
[[18:17:18]] [INFO] Refreshing screenshot...
[[18:17:18]] [INFO] kz9lnCdwoH=pass
[[18:17:14]] [SUCCESS] Screenshot refreshed successfully
[[18:17:14]] [SUCCESS] Screenshot refreshed successfully
[[18:17:14]] [INFO] kz9lnCdwoH=running
[[18:17:14]] [INFO] Executing action 45/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[18:17:13]] [SUCCESS] Screenshot refreshed
[[18:17:13]] [INFO] Refreshing screenshot...
[[18:17:13]] [INFO] kz9lnCdwoH=pass
[[18:17:09]] [SUCCESS] Screenshot refreshed successfully
[[18:17:09]] [SUCCESS] Screenshot refreshed successfully
[[18:17:09]] [INFO] kz9lnCdwoH=running
[[18:17:09]] [INFO] Executing action 44/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:17:09]] [SUCCESS] Screenshot refreshed
[[18:17:09]] [INFO] Refreshing screenshot...
[[18:17:09]] [INFO] qIF9CVPc56=pass
[[18:17:04]] [SUCCESS] Screenshot refreshed successfully
[[18:17:04]] [SUCCESS] Screenshot refreshed successfully
[[18:17:04]] [INFO] qIF9CVPc56=running
[[18:17:04]] [INFO] Executing action 43/576: iOS Function: text - Text: "mat"
[[18:17:04]] [SUCCESS] Screenshot refreshed
[[18:17:04]] [INFO] Refreshing screenshot...
[[18:17:04]] [INFO] yEga5MkcRe=pass
[[18:17:00]] [SUCCESS] Screenshot refreshed successfully
[[18:17:00]] [SUCCESS] Screenshot refreshed successfully
[[18:17:00]] [INFO] yEga5MkcRe=running
[[18:17:00]] [INFO] Executing action 42/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:17:00]] [SUCCESS] Screenshot refreshed
[[18:17:00]] [INFO] Refreshing screenshot...
[[18:17:00]] [INFO] F4NGh9HrLw=pass
[[18:16:56]] [SUCCESS] Screenshot refreshed successfully
[[18:16:56]] [SUCCESS] Screenshot refreshed successfully
[[18:16:55]] [INFO] F4NGh9HrLw=running
[[18:16:55]] [INFO] Executing action 41/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:16:55]] [SUCCESS] Screenshot refreshed
[[18:16:55]] [INFO] Refreshing screenshot...
[[18:16:55]] [INFO] kz9lnCdwoH=pass
[[18:16:51]] [SUCCESS] Screenshot refreshed successfully
[[18:16:51]] [SUCCESS] Screenshot refreshed successfully
[[18:16:50]] [INFO] kz9lnCdwoH=running
[[18:16:50]] [INFO] Executing action 40/576: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[18:16:50]] [SUCCESS] Screenshot refreshed
[[18:16:50]] [INFO] Refreshing screenshot...
[[18:16:50]] [INFO] kz9lnCdwoH=pass
[[18:16:46]] [SUCCESS] Screenshot refreshed successfully
[[18:16:46]] [SUCCESS] Screenshot refreshed successfully
[[18:16:46]] [INFO] kz9lnCdwoH=running
[[18:16:46]] [INFO] Executing action 39/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:16:45]] [SUCCESS] Screenshot refreshed
[[18:16:45]] [INFO] Refreshing screenshot...
[[18:16:45]] [INFO] JRheDTvpJf=pass
[[18:16:41]] [SUCCESS] Screenshot refreshed successfully
[[18:16:41]] [SUCCESS] Screenshot refreshed successfully
[[18:16:41]] [INFO] JRheDTvpJf=running
[[18:16:41]] [INFO] Executing action 38/576: iOS Function: text - Text: "Kid toy"
[[18:16:40]] [SUCCESS] Screenshot refreshed
[[18:16:40]] [INFO] Refreshing screenshot...
[[18:16:40]] [INFO] yEga5MkcRe=pass
[[18:16:37]] [SUCCESS] Screenshot refreshed successfully
[[18:16:37]] [SUCCESS] Screenshot refreshed successfully
[[18:16:36]] [INFO] yEga5MkcRe=running
[[18:16:36]] [INFO] Executing action 37/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:16:36]] [SUCCESS] Screenshot refreshed
[[18:16:36]] [INFO] Refreshing screenshot...
[[18:16:36]] [INFO] F4NGh9HrLw=pass
[[18:16:32]] [SUCCESS] Screenshot refreshed successfully
[[18:16:32]] [SUCCESS] Screenshot refreshed successfully
[[18:16:32]] [INFO] F4NGh9HrLw=running
[[18:16:32]] [INFO] Executing action 36/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:16:31]] [SUCCESS] Screenshot refreshed
[[18:16:31]] [INFO] Refreshing screenshot...
[[18:16:31]] [INFO] XPEr3w6Zof=pass
[[18:16:26]] [SUCCESS] Screenshot refreshed successfully
[[18:16:26]] [SUCCESS] Screenshot refreshed successfully
[[18:16:26]] [INFO] XPEr3w6Zof=running
[[18:16:26]] [INFO] Executing action 35/576: Restart app: env[appid]
[[18:16:26]] [SUCCESS] Screenshot refreshed
[[18:16:26]] [INFO] Refreshing screenshot...
[[18:16:26]] [INFO] PiQRBWBe3E=pass
[[18:16:22]] [SUCCESS] Screenshot refreshed successfully
[[18:16:22]] [SUCCESS] Screenshot refreshed successfully
[[18:16:22]] [INFO] PiQRBWBe3E=running
[[18:16:22]] [INFO] Executing action 34/576: Tap on image: env[device-back-img]
[[18:16:21]] [SUCCESS] Screenshot refreshed
[[18:16:21]] [INFO] Refreshing screenshot...
[[18:16:21]] [INFO] GWoppouz1l=pass
[[18:16:19]] [SUCCESS] Screenshot refreshed successfully
[[18:16:19]] [SUCCESS] Screenshot refreshed successfully
[[18:16:19]] [INFO] GWoppouz1l=running
[[18:16:19]] [INFO] Executing action 33/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[18:16:18]] [SUCCESS] Screenshot refreshed
[[18:16:18]] [INFO] Refreshing screenshot...
[[18:16:18]] [INFO] B6GDXWAmWp=pass
[[18:16:14]] [SUCCESS] Screenshot refreshed successfully
[[18:16:14]] [SUCCESS] Screenshot refreshed successfully
[[18:16:14]] [INFO] B6GDXWAmWp=running
[[18:16:14]] [INFO] Executing action 32/576: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[18:16:13]] [SUCCESS] Screenshot refreshed
[[18:16:13]] [INFO] Refreshing screenshot...
[[18:16:13]] [INFO] mtYqeDttRc=pass
[[18:16:09]] [SUCCESS] Screenshot refreshed successfully
[[18:16:09]] [SUCCESS] Screenshot refreshed successfully
[[18:16:09]] [INFO] mtYqeDttRc=running
[[18:16:09]] [INFO] Executing action 31/576: Tap on image: env[paypal-close-img]
[[18:16:09]] [SUCCESS] Screenshot refreshed
[[18:16:09]] [INFO] Refreshing screenshot...
[[18:16:09]] [INFO] q6cKxgMAIn=pass
[[18:16:03]] [SUCCESS] Screenshot refreshed successfully
[[18:16:03]] [SUCCESS] Screenshot refreshed successfully
[[18:16:02]] [INFO] q6cKxgMAIn=running
[[18:16:02]] [INFO] Executing action 30/576: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[18:16:02]] [SUCCESS] Screenshot refreshed
[[18:16:02]] [INFO] Refreshing screenshot...
[[18:16:02]] [INFO] KRQDBv2D3A=pass
[[18:15:58]] [SUCCESS] Screenshot refreshed successfully
[[18:15:58]] [SUCCESS] Screenshot refreshed successfully
[[18:15:58]] [INFO] KRQDBv2D3A=running
[[18:15:58]] [INFO] Executing action 29/576: Tap on image: env[device-back-img]
[[18:15:57]] [SUCCESS] Screenshot refreshed
[[18:15:57]] [INFO] Refreshing screenshot...
[[18:15:57]] [INFO] P4b2BITpCf=pass
[[18:15:54]] [SUCCESS] Screenshot refreshed successfully
[[18:15:54]] [SUCCESS] Screenshot refreshed successfully
[[18:15:54]] [INFO] P4b2BITpCf=running
[[18:15:54]] [INFO] Executing action 28/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[18:15:54]] [SUCCESS] Screenshot refreshed
[[18:15:54]] [INFO] Refreshing screenshot...
[[18:15:54]] [INFO] inrxgdWzXr=pass
[[18:15:48]] [SUCCESS] Screenshot refreshed successfully
[[18:15:48]] [SUCCESS] Screenshot refreshed successfully
[[18:15:47]] [INFO] inrxgdWzXr=running
[[18:15:47]] [INFO] Executing action 27/576: Tap on element with accessibility_id: Learn more about Zip
[[18:15:47]] [SUCCESS] Screenshot refreshed
[[18:15:47]] [INFO] Refreshing screenshot...
[[18:15:47]] [INFO] Et3kvnFdxh=pass
[[18:15:43]] [SUCCESS] Screenshot refreshed successfully
[[18:15:43]] [SUCCESS] Screenshot refreshed successfully
[[18:15:43]] [INFO] Et3kvnFdxh=running
[[18:15:43]] [INFO] Executing action 26/576: Tap on image: env[device-back-img]
[[18:15:43]] [INFO] Skipping disabled action 25/576: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[18:15:43]] [SUCCESS] Screenshot refreshed
[[18:15:43]] [INFO] Refreshing screenshot...
[[18:15:43]] [INFO] pk2DLZFBmx=pass
[[18:15:36]] [SUCCESS] Screenshot refreshed successfully
[[18:15:36]] [SUCCESS] Screenshot refreshed successfully
[[18:15:35]] [INFO] pk2DLZFBmx=running
[[18:15:35]] [INFO] Executing action 24/576: Tap on element with accessibility_id: Learn more about AfterPay
[[18:15:35]] [SUCCESS] Screenshot refreshed
[[18:15:35]] [INFO] Refreshing screenshot...
[[18:15:35]] [INFO] ShJSdXvmVL=pass
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [INFO] ShJSdXvmVL=running
[[18:15:28]] [INFO] Executing action 23/576: Swipe up till element accessibilityid: "Learn more about AfterPay" is visible
[[18:15:27]] [SUCCESS] Screenshot refreshed
[[18:15:27]] [INFO] Refreshing screenshot...
[[18:15:27]] [INFO] sHQtYzpI4s=pass
[[18:15:23]] [SUCCESS] Screenshot refreshed successfully
[[18:15:23]] [SUCCESS] Screenshot refreshed successfully
[[18:15:22]] [INFO] sHQtYzpI4s=running
[[18:15:22]] [INFO] Executing action 22/576: Tap on image: env[closebtnimage]
[[18:15:21]] [SUCCESS] Screenshot refreshed
[[18:15:21]] [INFO] Refreshing screenshot...
[[18:15:21]] [INFO] 83tV9A4NOn=pass
[[18:15:18]] [SUCCESS] Screenshot refreshed successfully
[[18:15:18]] [SUCCESS] Screenshot refreshed successfully
[[18:15:17]] [INFO] 83tV9A4NOn=running
[[18:15:17]] [INFO] Executing action 21/576: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[18:15:17]] [SUCCESS] Screenshot refreshed
[[18:15:17]] [INFO] Refreshing screenshot...
[[18:15:17]] [INFO] dCqKBG3e7u=pass
[[18:15:12]] [SUCCESS] Screenshot refreshed successfully
[[18:15:12]] [SUCCESS] Screenshot refreshed successfully
[[18:15:12]] [INFO] dCqKBG3e7u=running
[[18:15:12]] [INFO] Executing action 20/576: Tap on image: env[product-share-img]
[[18:15:11]] [SUCCESS] Screenshot refreshed
[[18:15:11]] [INFO] Refreshing screenshot...
[[18:15:11]] [INFO] kAQ1yIIw3h=pass
[[18:14:36]] [SUCCESS] Screenshot refreshed successfully
[[18:14:36]] [SUCCESS] Screenshot refreshed successfully
[[18:14:35]] [INFO] kAQ1yIIw3h=running
[[18:14:35]] [INFO] Executing action 19/576: Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* with fallback: Coordinates (98, 308)
[[18:14:35]] [SUCCESS] Screenshot refreshed
[[18:14:35]] [INFO] Refreshing screenshot...
[[18:14:35]] [INFO] OmKfD9iBjD=pass
[[18:14:31]] [SUCCESS] Screenshot refreshed successfully
[[18:14:31]] [SUCCESS] Screenshot refreshed successfully
[[18:14:31]] [INFO] OmKfD9iBjD=running
[[18:14:31]] [INFO] Executing action 18/576: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[18:14:30]] [SUCCESS] Screenshot refreshed
[[18:14:30]] [INFO] Refreshing screenshot...
[[18:14:30]] [INFO] dMl1PH9Dlc=pass
[[18:14:19]] [SUCCESS] Screenshot refreshed successfully
[[18:14:19]] [SUCCESS] Screenshot refreshed successfully
[[18:14:18]] [INFO] dMl1PH9Dlc=running
[[18:14:18]] [INFO] Executing action 17/576: Wait for 10 ms
[[18:14:18]] [SUCCESS] Screenshot refreshed
[[18:14:18]] [INFO] Refreshing screenshot...
[[18:14:18]] [INFO] eHLWiRoqqS=pass
[[18:14:13]] [SUCCESS] Screenshot refreshed successfully
[[18:14:13]] [SUCCESS] Screenshot refreshed successfully
[[18:14:13]] [INFO] eHLWiRoqqS=running
[[18:14:13]] [INFO] Executing action 16/576: Swipe from (50%, 70%) to (50%, 30%)
[[18:14:12]] [SUCCESS] Screenshot refreshed
[[18:14:12]] [INFO] Refreshing screenshot...
[[18:14:12]] [INFO] huUnpMMjVR=pass
[[18:14:08]] [SUCCESS] Screenshot refreshed successfully
[[18:14:08]] [SUCCESS] Screenshot refreshed successfully
[[18:14:08]] [INFO] huUnpMMjVR=running
[[18:14:08]] [INFO] Executing action 15/576: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[18:14:07]] [SUCCESS] Screenshot refreshed
[[18:14:07]] [INFO] Refreshing screenshot...
[[18:14:07]] [INFO] XmAxcBtFI0=pass
[[18:14:03]] [SUCCESS] Screenshot refreshed successfully
[[18:14:03]] [SUCCESS] Screenshot refreshed successfully
[[18:14:03]] [INFO] XmAxcBtFI0=running
[[18:14:03]] [INFO] Executing action 14/576: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[18:14:03]] [SUCCESS] Screenshot refreshed
[[18:14:03]] [INFO] Refreshing screenshot...
[[18:14:03]] [INFO] ktAufkDJnF=pass
[[18:13:59]] [SUCCESS] Screenshot refreshed successfully
[[18:13:59]] [SUCCESS] Screenshot refreshed successfully
[[18:13:59]] [INFO] ktAufkDJnF=running
[[18:13:59]] [INFO] Executing action 13/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show")]
[[18:13:58]] [SUCCESS] Screenshot refreshed
[[18:13:58]] [INFO] Refreshing screenshot...
[[18:13:58]] [INFO] a50JhCx0ir=pass
[[18:13:54]] [SUCCESS] Screenshot refreshed successfully
[[18:13:54]] [SUCCESS] Screenshot refreshed successfully
[[18:13:54]] [INFO] a50JhCx0ir=running
[[18:13:54]] [INFO] Executing action 12/576: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[18:13:54]] [SUCCESS] Screenshot refreshed
[[18:13:54]] [INFO] Refreshing screenshot...
[[18:13:54]] [INFO] Y1O1clhMSJ=pass
[[18:13:50]] [SUCCESS] Screenshot refreshed successfully
[[18:13:50]] [SUCCESS] Screenshot refreshed successfully
[[18:13:49]] [INFO] Y1O1clhMSJ=running
[[18:13:49]] [INFO] Executing action 11/576: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[18:13:49]] [SUCCESS] Screenshot refreshed
[[18:13:49]] [INFO] Refreshing screenshot...
[[18:13:49]] [INFO] lYPskZt0Ya=pass
[[18:13:45]] [SUCCESS] Screenshot refreshed successfully
[[18:13:45]] [SUCCESS] Screenshot refreshed successfully
[[18:13:45]] [INFO] lYPskZt0Ya=running
[[18:13:45]] [INFO] Executing action 10/576: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:13:45]] [SUCCESS] Screenshot refreshed
[[18:13:45]] [INFO] Refreshing screenshot...
[[18:13:45]] [INFO] xUbWFa8Ok2=pass
[[18:13:40]] [SUCCESS] Screenshot refreshed successfully
[[18:13:40]] [SUCCESS] Screenshot refreshed successfully
[[18:13:40]] [INFO] xUbWFa8Ok2=running
[[18:13:40]] [INFO] Executing action 9/576: Tap on Text: "Latest"
[[18:13:39]] [SUCCESS] Screenshot refreshed
[[18:13:39]] [INFO] Refreshing screenshot...
[[18:13:39]] [INFO] RbNtEW6N9T=pass
[[18:13:35]] [SUCCESS] Screenshot refreshed successfully
[[18:13:35]] [SUCCESS] Screenshot refreshed successfully
[[18:13:35]] [INFO] RbNtEW6N9T=running
[[18:13:35]] [INFO] Executing action 8/576: Tap on Text: "Toys"
[[18:13:34]] [SUCCESS] Screenshot refreshed
[[18:13:34]] [INFO] Refreshing screenshot...
[[18:13:34]] [INFO] ltDXyWvtEz=pass
[[18:13:30]] [SUCCESS] Screenshot refreshed successfully
[[18:13:30]] [SUCCESS] Screenshot refreshed successfully
[[18:13:30]] [INFO] ltDXyWvtEz=running
[[18:13:30]] [INFO] Executing action 7/576: Tap on image: env[device-back-img]
[[18:13:30]] [SUCCESS] Screenshot refreshed
[[18:13:30]] [INFO] Refreshing screenshot...
[[18:13:30]] [INFO] QPKR6jUF9O=pass
[[18:13:27]] [SUCCESS] Screenshot refreshed successfully
[[18:13:27]] [SUCCESS] Screenshot refreshed successfully
[[18:13:27]] [INFO] QPKR6jUF9O=running
[[18:13:27]] [INFO] Executing action 6/576: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[18:13:27]] [SUCCESS] Screenshot refreshed
[[18:13:27]] [INFO] Refreshing screenshot...
[[18:13:27]] [INFO] vfwUVEyq6X=pass
[[18:13:24]] [SUCCESS] Screenshot refreshed successfully
[[18:13:24]] [SUCCESS] Screenshot refreshed successfully
[[18:13:24]] [INFO] vfwUVEyq6X=running
[[18:13:24]] [INFO] Executing action 5/576: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[18:13:23]] [SUCCESS] Screenshot refreshed
[[18:13:23]] [INFO] Refreshing screenshot...
[[18:13:23]] [INFO] Xr6F8gdd8q=pass
[[18:13:19]] [SUCCESS] Screenshot refreshed successfully
[[18:13:19]] [SUCCESS] Screenshot refreshed successfully
[[18:13:19]] [INFO] Xr6F8gdd8q=running
[[18:13:19]] [INFO] Executing action 4/576: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:13:19]] [SUCCESS] Screenshot refreshed
[[18:13:19]] [INFO] Refreshing screenshot...
[[18:13:19]] [INFO] Xr6F8gdd8q=pass
[[18:13:16]] [SUCCESS] Screenshot refreshed successfully
[[18:13:16]] [SUCCESS] Screenshot refreshed successfully
[[18:13:16]] [INFO] Xr6F8gdd8q=running
[[18:13:16]] [INFO] Executing action 3/576: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[18:13:15]] [SUCCESS] Screenshot refreshed
[[18:13:15]] [INFO] Refreshing screenshot...
[[18:13:15]] [INFO] F4NGh9HrLw=pass
[[18:13:13]] [INFO] Collapsed all test cases
[[18:13:12]] [SUCCESS] Screenshot refreshed successfully
[[18:13:12]] [SUCCESS] Screenshot refreshed successfully
[[18:13:11]] [INFO] F4NGh9HrLw=running
[[18:13:11]] [INFO] Executing action 2/576: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:13:11]] [SUCCESS] Screenshot refreshed
[[18:13:11]] [INFO] Refreshing screenshot...
[[18:13:11]] [INFO] H9fy9qcFbZ=pass
[[18:13:06]] [INFO] H9fy9qcFbZ=running
[[18:13:06]] [INFO] Executing action 1/576: Restart app: env[appid]
[[18:13:06]] [INFO] ExecutionManager: Starting execution of 576 actions...
[[18:13:06]] [SUCCESS] Cleared 1 screenshots from database
[[18:13:06]] [INFO] Clearing screenshots from database before execution...
[[18:13:06]] [SUCCESS] All screenshots deleted successfully
[[18:13:06]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:13:06]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250627_181306/screenshots
[[18:13:06]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250627_181306
[[18:13:06]] [SUCCESS] Report directory initialized successfully
[[18:13:06]] [INFO] Initializing report directory and screenshots folder for test suite...
[[18:13:02]] [SUCCESS] All screenshots deleted successfully
[[18:13:02]] [INFO] All actions cleared
[[18:13:02]] [INFO] Cleaning up screenshots...
[[18:12:58]] [SUCCESS] Screenshot refreshed successfully
[[18:12:57]] [SUCCESS] Screenshot refreshed
[[18:12:57]] [INFO] Refreshing screenshot...
[[18:12:56]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[18:12:56]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[18:12:54]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[18:11:46]] [SUCCESS] Found 1 device(s)
[[18:11:45]] [INFO] Refreshing device list...
