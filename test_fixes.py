#!/usr/bin/env python3
"""
Test script to validate the fixes for UI status update and export report issues
"""

import sys
import os
sys.path.append('.')

def test_ui_status_update():
    """Test the UI status update fix"""
    print("=== Testing UI Status Update Fix ===")
    
    from app.utils.database import get_final_test_case_status
    
    # Test with the known test case that has retry updates
    suite_id = "testsuite_execution_20250627_181306"
    filename = "Delivery & CNC Stop"
    
    print(f"Testing status retrieval for suite: {suite_id}, test case: {filename}")
    
    status_data = get_final_test_case_status(
        suite_id=suite_id,
        filename=filename
    )
    
    print(f"Final status: {status_data.get('status', 'unknown')}")
    print(f"Number of actions: {len(status_data.get('actions', []))}")
    
    # Print action details
    for action_id, action_info in status_data.get('actions', {}).items():
        print(f"  Action {action_id}: {action_info.get('status', 'unknown')} (type: {action_info.get('action_type', 'unknown')})")
    
    return status_data.get('status') == 'passed'

def test_export_report_generation():
    """Test the export report generation fix"""
    print("\n=== Testing Export Report Generation Fix ===")
    
    from app.utils.custom_report_generator import CustomReportGenerator
    
    # Test with the known execution ID
    execution_id = "testsuite_execution_20250627_181306"
    app_root_path = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app"
    
    print(f"Testing export generation for execution: {execution_id}")
    
    try:
        generator = CustomReportGenerator(execution_id, app_root_path)
        
        # Test data loading
        test_data = generator._load_test_data()
        
        if test_data:
            print(f"Successfully loaded test data")
            print(f"Test suite name: {test_data.get('name', 'Unknown')}")
            print(f"Number of test cases: {len(test_data.get('testCases', []))}")
            
            # Print test case details
            for i, tc in enumerate(test_data.get('testCases', [])):
                print(f"  Test Case {i+1}: {tc.get('name', 'Unknown')} - Status: {tc.get('status', 'Unknown')}")
                print(f"    Number of steps: {len(tc.get('steps', []))}")
                
                # Check for 'unknown' statuses in steps
                unknown_steps = [step for step in tc.get('steps', []) if step.get('status') == 'Unknown']
                if unknown_steps:
                    print(f"    WARNING: {len(unknown_steps)} steps have 'Unknown' status")
                else:
                    print(f"    All steps have proper status")
            
            return len(test_data.get('testCases', [])) > 1  # Should have multiple test cases
        else:
            print("Failed to load test data")
            return False
            
    except Exception as e:
        print(f"Error testing export generation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_reconstruction():
    """Test the database reconstruction functionality"""
    print("\n=== Testing Database Reconstruction ===")
    
    from app.utils.custom_report_generator import CustomReportGenerator
    
    execution_id = "testsuite_execution_20250627_181306"
    app_root_path = "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app"
    
    try:
        generator = CustomReportGenerator(execution_id, app_root_path)
        
        # Test database reconstruction directly
        reconstructed_data = generator._reconstruct_test_data_from_database()
        
        if reconstructed_data:
            print(f"Successfully reconstructed data from database")
            print(f"Test suite ID: {reconstructed_data.get('id', 'Unknown')}")
            print(f"Test suite name: {reconstructed_data.get('name', 'Unknown')}")
            print(f"Number of test cases: {len(reconstructed_data.get('testCases', []))}")
            
            # Check if all test cases have proper status
            all_proper_status = True
            for tc in reconstructed_data.get('testCases', []):
                if tc.get('status') == 'Unknown':
                    all_proper_status = False
                    print(f"  WARNING: Test case {tc.get('name')} has Unknown status")
                else:
                    print(f"  Test case {tc.get('name')}: {tc.get('status')}")
            
            return all_proper_status and len(reconstructed_data.get('testCases', [])) > 0
        else:
            print("Failed to reconstruct data from database")
            return False
            
    except Exception as e:
        print(f"Error testing database reconstruction: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Testing fixes for UI status update and export report issues\n")
    
    # Test 1: UI Status Update
    ui_test_passed = test_ui_status_update()
    
    # Test 2: Export Report Generation
    export_test_passed = test_export_report_generation()
    
    # Test 3: Database Reconstruction
    db_test_passed = test_database_reconstruction()
    
    # Summary
    print("\n=== Test Results Summary ===")
    print(f"UI Status Update Fix: {'PASSED' if ui_test_passed else 'FAILED'}")
    print(f"Export Report Generation Fix: {'PASSED' if export_test_passed else 'FAILED'}")
    print(f"Database Reconstruction Fix: {'PASSED' if db_test_passed else 'FAILED'}")
    
    all_passed = ui_test_passed and export_test_passed and db_test_passed
    print(f"\nOverall Result: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
