#!/usr/bin/env python3
"""
Analyze the current database schema and UUID usage
"""
import sqlite3
import os
import json

def get_db_path():
    """Get the database path"""
    base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    return os.path.join(base_path, 'test_execution.db')

def analyze_database_schema():
    """Analyze the current database schema and UUID usage"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"Database not found at: {db_path}")
        return
    
    print(f"Analyzing database at: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get table schema
        print("\n=== EXECUTION_TRACKING TABLE SCHEMA ===")
        cursor.execute("PRAGMA table_info(execution_tracking)")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"Column: {col['name']:<20} Type: {col['type']:<15} NotNull: {col['notnull']:<5} Default: {col['dflt_value']}")
        
        # Get sample data to understand current UUID usage
        print("\n=== SAMPLE DATA (First 5 Records) ===")
        cursor.execute("SELECT * FROM execution_tracking LIMIT 5")
        sample_records = cursor.fetchall()
        
        if sample_records:
            # Print column headers
            headers = [description[0] for description in cursor.description]
            print(" | ".join(f"{header:<15}" for header in headers))
            print("-" * (len(headers) * 17))
            
            for record in sample_records:
                values = []
                for value in record:
                    if value is None:
                        values.append("NULL")
                    elif isinstance(value, str) and len(value) > 15:
                        values.append(value[:12] + "...")
                    else:
                        values.append(str(value))
                print(" | ".join(f"{value:<15}" for value in values))
        
        # Analyze UUID patterns
        print("\n=== UUID ANALYSIS ===")
        
        # Check suite_id patterns
        cursor.execute("SELECT DISTINCT suite_id FROM execution_tracking")
        suite_ids = cursor.fetchall()
        print(f"\nUnique suite_ids ({len(suite_ids)}):")
        for suite_id in suite_ids[:10]:  # Show first 10
            print(f"  {suite_id['suite_id']}")
        if len(suite_ids) > 10:
            print(f"  ... and {len(suite_ids) - 10} more")
        
        # Check test_case_id patterns
        cursor.execute("SELECT DISTINCT test_case_id FROM execution_tracking WHERE test_case_id IS NOT NULL")
        test_case_ids = cursor.fetchall()
        print(f"\nUnique test_case_ids ({len(test_case_ids)}):")
        for test_case_id in test_case_ids[:10]:  # Show first 10
            print(f"  {test_case_id['test_case_id']}")
        if len(test_case_ids) > 10:
            print(f"  ... and {len(test_case_ids) - 10} more")
        
        # Check action_id patterns
        cursor.execute("SELECT DISTINCT action_id FROM execution_tracking WHERE action_id IS NOT NULL")
        action_ids = cursor.fetchall()
        print(f"\nUnique action_ids ({len(action_ids)}):")
        for action_id in action_ids[:10]:  # Show first 10
            print(f"  {action_id['action_id']}")
        if len(action_ids) > 10:
            print(f"  ... and {len(action_ids) - 10} more")
        
        # Check filename patterns
        cursor.execute("SELECT DISTINCT filename FROM execution_tracking WHERE filename IS NOT NULL")
        filenames = cursor.fetchall()
        print(f"\nUnique filenames ({len(filenames)}):")
        for filename in filenames[:10]:  # Show first 10
            print(f"  {filename['filename']}")
        if len(filenames) > 10:
            print(f"  ... and {len(filenames) - 10} more")
        
        # Check for NULL values in key fields
        print("\n=== NULL VALUE ANALYSIS ===")
        
        cursor.execute("SELECT COUNT(*) as total FROM execution_tracking")
        total_records = cursor.fetchone()['total']
        print(f"Total records: {total_records}")
        
        cursor.execute("SELECT COUNT(*) as null_count FROM execution_tracking WHERE suite_id IS NULL")
        null_suite_ids = cursor.fetchone()['null_count']
        print(f"NULL suite_id: {null_suite_ids} ({null_suite_ids/total_records*100:.1f}%)")
        
        cursor.execute("SELECT COUNT(*) as null_count FROM execution_tracking WHERE test_case_id IS NULL")
        null_test_case_ids = cursor.fetchone()['null_count']
        print(f"NULL test_case_id: {null_test_case_ids} ({null_test_case_ids/total_records*100:.1f}%)")
        
        cursor.execute("SELECT COUNT(*) as null_count FROM execution_tracking WHERE action_id IS NULL")
        null_action_ids = cursor.fetchone()['null_count']
        print(f"NULL action_id: {null_action_ids} ({null_action_ids/total_records*100:.1f}%)")
        
        cursor.execute("SELECT COUNT(*) as null_count FROM execution_tracking WHERE filename IS NULL")
        null_filenames = cursor.fetchone()['null_count']
        print(f"NULL filename: {null_filenames} ({null_filenames/total_records*100:.1f}%)")
        
        conn.close()
        
    except Exception as e:
        print(f"Error analyzing database: {str(e)}")

def analyze_test_case_json_structure():
    """Analyze test case JSON files to understand UUID structure"""
    print("\n" + "="*60)
    print("ANALYZING TEST CASE JSON STRUCTURE")
    print("="*60)

    # Look for test case files in the configured directory
    test_cases_dir = "/Users/<USER>/Documents/automation-tool/test_cases"
    if os.path.exists(test_cases_dir):
        print(f"\nFound test_cases directory: {test_cases_dir}")

        json_files = [f for f in os.listdir(test_cases_dir) if f.endswith('.json')]
        print(f"Found {len(json_files)} JSON files")

        if json_files:
            # Analyze first few test case files
            for i, json_file in enumerate(json_files[:3]):
                print(f"\n--- Analyzing {json_file} ---")
                try:
                    with open(os.path.join(test_cases_dir, json_file), 'r') as f:
                        test_case_data = json.load(f)

                    print(f"Keys in test case: {list(test_case_data.keys())}")

                    if 'test_case_id' in test_case_data:
                        print(f"test_case_id: {test_case_data['test_case_id']}")

                    if 'actions' in test_case_data:
                        print(f"Number of actions: {len(test_case_data['actions'])}")
                        if test_case_data['actions']:
                            first_action = test_case_data['actions'][0]
                            print(f"First action keys: {list(first_action.keys())}")
                            if 'action_id' in first_action:
                                print(f"First action_id: {first_action['action_id']}")

                except Exception as e:
                    print(f"Error reading {json_file}: {e}")
    else:
        print(f"test_cases directory not found at: {test_cases_dir}")

    # Also check test suites directory
    test_suites_dir = "/Users/<USER>/Documents/automation-tool/test_suites"
    if os.path.exists(test_suites_dir):
        print(f"\n--- ANALYZING TEST SUITES ---")
        print(f"Found test_suites directory: {test_suites_dir}")

        json_files = [f for f in os.listdir(test_suites_dir) if f.endswith('.json')]
        print(f"Found {len(json_files)} JSON files")

        if json_files:
            # Analyze first test suite file
            json_file = json_files[0]
            print(f"\n--- Analyzing {json_file} ---")
            try:
                with open(os.path.join(test_suites_dir, json_file), 'r') as f:
                    test_suite_data = json.load(f)

                print(f"Keys in test suite: {list(test_suite_data.keys())}")

                if 'test_suite_id' in test_suite_data:
                    print(f"test_suite_id: {test_suite_data['test_suite_id']}")

                if 'test_cases' in test_suite_data:
                    print(f"Number of test cases: {len(test_suite_data['test_cases'])}")
                    if test_suite_data['test_cases']:
                        first_test_case = test_suite_data['test_cases'][0]
                        print(f"First test case keys: {list(first_test_case.keys())}")
                        if 'test_case_id' in first_test_case:
                            print(f"First test_case_id: {first_test_case['test_case_id']}")

            except Exception as e:
                print(f"Error reading {json_file}: {e}")
    else:
        print(f"test_suites directory not found at: {test_suites_dir}")

if __name__ == "__main__":
    analyze_database_schema()
    analyze_test_case_json_structure()
